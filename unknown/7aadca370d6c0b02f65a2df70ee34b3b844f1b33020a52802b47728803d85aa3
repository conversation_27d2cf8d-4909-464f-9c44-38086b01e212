#!/usr/bin/env python3
"""
Debug city names in the data
"""

import sys
from pathlib import Path
from collections import Counter

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def check_city_names():
    """Check what city names are actually in the data"""
    print("🏙️ Checking city names in data...")
    
    try:
        from src.core.data_loader import load_raw
        programs = load_raw()
        
        if not programs:
            print("❌ No programs loaded")
            return
            
        print(f"📊 Loaded {len(programs)} programs")
        
        # Count city names
        cities = []
        for program in programs:
            city = program.get('city', '').strip()
            if city:
                cities.append(city.lower())
                
        city_counts = Counter(cities)
        
        print(f"\n🏙️ City distribution:")
        for city, count in city_counts.most_common(10):
            print(f"   {city}: {count} programs")
            
        # Check for Battambang variations
        print(f"\n🔍 Battambang variations:")
        battambang_cities = [city for city in cities if 'battambang' in city or 'btb' in city or 'បាត់ដំបង' in city]
        battambang_counts = Counter(battambang_cities)
        for city, count in battambang_counts.items():
            print(f"   {city}: {count} programs")
            
        # Check for Siem Reap variations
        print(f"\n🔍 Siem Reap variations:")
        sr_cities = [city for city in cities if 'siem' in city or 'reap' in city or 'sr' in city or 'សៀមរាប' in city]
        sr_counts = Counter(sr_cities)
        for city, count in sr_counts.items():
            print(f"   {city}: {count} programs")
            
        # Check for Phnom Penh variations
        print(f"\n🔍 Phnom Penh variations:")
        pp_cities = [city for city in cities if 'phnom' in city or 'penh' in city or 'pp' in city or 'ភ្នំពេញ' in city]
        pp_counts = Counter(pp_cities)
        for city, count in pp_counts.items():
            print(f"   {city}: {count} programs")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    check_city_names()
