#!/usr/bin/env python3
"""
Test the location filtering fix
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_location_filtering():
    """Test the fixed location filtering"""
    print("🏙️ Testing location filtering fix...")
    
    try:
        from src.core.recommender import filter_majors_by_location, get_recommendations
        from src.core.data_loader import load_raw
        
        # Load all programs
        all_programs = load_raw()
        print(f"📊 Loaded {len(all_programs)} total programs")
        
        # Test each location
        locations = ['phnom_penh', 'siem_reap', 'battambang']
        
        for location in locations:
            print(f"\n🔍 Testing location: {location}")
            
            # Test filtering
            filtered = filter_majors_by_location(all_programs, location)
            print(f"   ✅ Filtered: {len(filtered)} programs")
            
            if filtered:
                sample_cities = [prog.get('city', 'N/A') for prog in filtered[:3]]
                print(f"   📍 Sample cities: {sample_cities}")
            else:
                print(f"   ❌ No programs found for {location}")
                
        # Test with actual recommendation system
        print(f"\n🎯 Testing with recommendation system...")
        
        for location in locations:
            print(f"\n🔍 Testing recommendations for: {location}")
            
            # Create assessment with specific location
            assessment = {
                0: {'answer_index': 2, 'answer_text': 'Computer Science'},
                1: {'answer_index': 1, 'answer_text': 'Medium'},
                2: {'answer_index': 0 if location == 'phnom_penh' else 1 if location == 'siem_reap' else 2, 'answer_text': location},
            }
            
            recommendations = await get_recommendations(assessment)
            print(f"   ✅ Generated: {len(recommendations)} recommendations")
            
            if recommendations:
                sample_rec = recommendations[0]
                city = sample_rec.get('city', 'N/A')
                major_id = sample_rec.get('major_id', 'N/A')
                print(f"   📍 Top recommendation city: {city}")
                print(f"   🆔 Major ID: {major_id}")
            else:
                print(f"   ❌ No recommendations for {location}")
                
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_detail_buttons():
    """Test the detail button callback data"""
    print("\n🔘 Testing detail button callback data...")
    
    try:
        from src.core.recommender import get_recommendations
        
        # Generate recommendations
        assessment = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},
            1: {'answer_index': 1, 'answer_text': 'Medium'},
            2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
        }
        
        recommendations = await get_recommendations(assessment)
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        # Test button creation logic
        for i, rec in enumerate(recommendations[:5]):
            major_id = rec.get('major_id', f'unknown_{i}')
            callback_data = f"details_{major_id}"
            
            print(f"   Button {i+1}: {callback_data}")
            
            # Verify major_id exists and is valid
            if major_id and major_id != f'unknown_{i}':
                print(f"     ✅ Valid major_id: {major_id}")
            else:
                print(f"     ❌ Invalid major_id: {major_id}")
                
        return True
        
    except Exception as e:
        print(f"❌ Detail button test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Testing Location and Detail Button Fixes\n")
    
    test1 = await test_location_filtering()
    test2 = await test_detail_buttons()
    
    print(f"\n📊 Test Results:")
    print(f"   Location Filtering: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   Detail Button Data: {'✅ PASS' if test2 else '❌ FAIL'}")
    
    overall_pass = test1 and test2
    print(f"\n🎯 Overall Status: {'✅ FIXES WORKING' if overall_pass else '❌ FIXES FAILED'}")
    
    if overall_pass:
        print("\n🎉 Both fixes are working!")
        print("✅ Location filtering now works with Khmer city names")
        print("✅ Detail buttons now use proper major IDs")
    else:
        print("\n⚠️  Some fixes still need work.")
    
    return overall_pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
