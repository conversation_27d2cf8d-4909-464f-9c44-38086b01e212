#!/usr/bin/env python3
"""
Test the production fixes for type conversion and error handling
"""

import sys
import asyncio
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_employment_rate_conversion():
    """Test employment rate conversion with various problematic formats"""
    print("🧪 Testing employment rate conversion...")
    
    def safe_convert_employment_rate(value):
        """Safe employment rate conversion function"""
        try:
            if isinstance(value, str):
                value_str = value.strip().replace('%', '')
                if value_str.isdigit():
                    return float(value_str)
                elif value_str.lower() in ['high', 'excellent', 'very good']:
                    return 85
                elif value_str.lower() in ['medium', 'good', 'average']:
                    return 70
                elif value_str.lower() in ['low', 'poor', 'below average']:
                    return 50
                else:
                    import re
                    numbers = re.findall(r'\d+\.?\d*', value_str)
                    return float(numbers[0]) if numbers else 50
            else:
                return float(value) if value is not None else 50
        except (ValueError, TypeError, AttributeError):
            return 50

    # Test problematic cases that were causing crashes
    test_cases = [
        ("90%", 90.0),
        ("85", 85.0),
        ("high", 85.0),
        ("excellent", 85.0),
        ("medium", 70.0),
        ("low", 50.0),
        ("95.5%", 95.5),
        ("N/A", 50.0),
        ("", 50.0),
        (None, 50.0),
        (88, 88.0),
        (0.85, 0.85),
        ("very good", 85.0),
        ("mixed 75% rate", 75.0)
    ]

    all_passed = True
    for test_value, expected in test_cases:
        try:
            result = safe_convert_employment_rate(test_value)
            if abs(result - expected) < 0.1:  # Allow small floating point differences
                print(f" ✅ {repr(test_value)} -> {result} (expected {expected})")
            else:
                print(f" ❌ {repr(test_value)} -> {result} (expected {expected})")
                all_passed = False
        except Exception as e:
            print(f" ❌ {repr(test_value)} -> ERROR: {e}")
            all_passed = False

    return all_passed


async def test_mcda_scorer_safety():
    """Test MCDA scorer with problematic data"""
    print("\n🔧 Testing MCDA scorer safety...")
    
    try:
        from src.core.mcda.scorer import calculate_mcda_score
        
        # Create test user answers
        user_answers = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},
            1: {'answer_index': 1, 'answer_text': 'Medium'},
            2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
        }
        
        # Test with problematic major data
        problematic_majors = [
            {
                'major_id': 'test1',
                'employment_rate': '90%',  # String percentage
                'tuition_fees_usd': '1500',  # String number
                'city': 'Phnom Penh'
            },
            {
                'major_id': 'test2',
                'employment_rate': 'high',  # Descriptive string
                'tuition_fees_usd': 2000,  # Integer
                'city': 'Siem Reap'
            },
            {
                'major_id': 'test3',
                'employment_rate': None,  # None value
                'tuition_fees_usd': None,  # None value
                'city': None
            },
            {
                'major_id': 'test4',
                # Missing fields entirely
            }
        ]
        
        all_passed = True
        for i, major in enumerate(problematic_majors):
            try:
                score = calculate_mcda_score(user_answers, major)
                if isinstance(score, (int, float)) and 0 <= score <= 1:
                    print(f" ✅ Major {i+1}: score = {score:.3f}")
                else:
                    print(f" ❌ Major {i+1}: invalid score = {score}")
                    all_passed = False
            except Exception as e:
                print(f" ❌ Major {i+1}: ERROR = {e}")
                all_passed = False
                
        return all_passed
        
    except Exception as e:
        print(f" ❌ MCDA scorer test failed: {e}")
        return False


async def test_recommendations_handler():
    """Test recommendations handler with mock data"""
    print("\n🤖 Testing recommendations handler...")
    
    try:
        from src.bot.handlers.recommendations import show_recommendations
        from telegram import Update, Message, Chat, User
        from telegram.ext import ContextTypes
        
        # Create mock objects
        chat = MagicMock(spec=Chat)
        chat.id = 123456789
        
        user = MagicMock(spec=User)
        user.id = 123456789
        
        message = MagicMock(spec=Message)
        message.chat = chat
        message.from_user = user
        
        update = AsyncMock(spec=Update)
        update.effective_chat = chat
        update.effective_user = user
        update.message = message
        update.callback_query = None
        
        context = AsyncMock(spec=ContextTypes.DEFAULT_TYPE)
        context.user_data = {
            'assessment': {
                0: {'answer_index': 2, 'answer_text': 'Computer Science'},
                1: {'answer_index': 1, 'answer_text': 'Medium'},
                2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
                'lang': 'kh'
            }
        }
        
        # Mock reply_text
        sent_messages = []
        async def mock_reply_text(text, reply_markup=None, parse_mode=None):
            sent_messages.append({'text': text, 'reply_markup': reply_markup})
            return MagicMock()
        message.reply_text = mock_reply_text
        
        # Test the handler
        result = await show_recommendations(update, context)
        
        if result is not None and sent_messages:
            print(f" ✅ Handler completed successfully, returned state: {result}")
            print(f" ✅ Sent {len(sent_messages)} messages")
            return True
        else:
            print(f" ❌ Handler failed: result={result}, messages={len(sent_messages)}")
            return False
            
    except Exception as e:
        print(f" ❌ Recommendations handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_telegram_safe_operations():
    """Test Telegram safe operations"""
    print("\n📱 Testing Telegram safe operations...")
    
    try:
        from src.bot.telegram_safe_v3 import safe_answer_callback, safe_edit_message, safe_send_message
        from telegram import CallbackQuery, Update, Message, Chat, User
        
        # Create mock callback query
        query = AsyncMock(spec=CallbackQuery)
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.message = MagicMock()
        query.message.text = "old text"
        query.message.reply_markup = None
        
        # Test safe_answer_callback
        result1 = await safe_answer_callback(query, "Test message")
        print(f" ✅ safe_answer_callback: {result1}")
        
        # Test safe_edit_message
        result2 = await safe_edit_message(query, "New text")
        print(f" ✅ safe_edit_message: {result2}")
        
        # Test with None query (should not crash)
        result3 = await safe_answer_callback(None)
        print(f" ✅ safe_answer_callback with None: {result3}")
        
        return True
        
    except Exception as e:
        print(f" ❌ Telegram safe operations test failed: {e}")
        return False


async def main():
    """Run all production fix tests"""
    print("🚀 Testing EduGuideBot Production Fixes\n")
    
    # Test 1: Employment rate conversion
    test1 = await test_employment_rate_conversion()
    
    # Test 2: MCDA scorer safety
    test2 = await test_mcda_scorer_safety()
    
    # Test 3: Recommendations handler
    test3 = await test_recommendations_handler()
    
    # Test 4: Telegram safe operations
    test4 = await test_telegram_safe_operations()
    
    print(f"\n📊 Production Fix Test Results:")
    print(f"   Employment Rate Conversion: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   MCDA Scorer Safety: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"   Recommendations Handler: {'✅ PASS' if test3 else '❌ FAIL'}")
    print(f"   Telegram Safe Operations: {'✅ PASS' if test4 else '❌ FAIL'}")
    
    all_passed = test1 and test2 and test3 and test4
    print(f"\n🎯 Overall Status: {'✅ ALL FIXES WORKING' if all_passed else '❌ SOME FIXES FAILED'}")
    
    if all_passed:
        print("\n🎉 Production fixes are working! Bot should be crash-free now.")
    else:
        print("\n⚠️  Some fixes need attention before running the bot.")
    
    return all_passed


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
