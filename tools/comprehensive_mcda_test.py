#!/usr/bin/env python3
"""
Comprehensive MCDA Scoring Accuracy Test
Tests the MCDA system with diverse scenarios and edge cases.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.core.mcda.scorer import calculate_mcda_score

def test_comprehensive_mcda_accuracy():
    """Test MCDA scoring accuracy with comprehensive scenarios."""
    print('🔍 COMPREHENSIVE MCDA SCORING ACCURACY TEST')
    print('=' * 60)
    
    # Define diverse test personas
    test_personas = [
        {
            'name': 'Tech-Savvy Student (PP)',
            'answers': {
                0: {'answer_index': 0, 'answer_text': 'STEM'},  # STEM interest (index 0)
                1: {'answer_index': 1, 'answer_text': 'ល្អ'},  # Good grades
                2: {'answer_index': 0, 'answer_text': 'ភ្នំពេញ'},  # Phnom Penh
                3: {'answer_index': 2, 'answer_text': '$1000-$2000'},  # Mid budget
                4: {'answer_index': 1, 'answer_text': 'ឯកជន'},  # Private uni
                5: {'answer_index': 0, 'answer_text': 'ប្រាក់ខែខ្ពស់'},  # High salary
                6: {'answer_index': 1, 'answer_text': 'បាន'},  # Has experience
                7: {'answer_index': 0, 'answer_text': 'ចាំបាច់'},  # Important
                8: {'answer_index': 1, 'answer_text': 'ចាំបាច់'},  # Important
                9: {'answer_index': 0, 'answer_text': 'រៀនផ្ទាល់ខ្លួន'}  # In-person
            }
        },
        {
            'name': 'Budget-Conscious Student (SR)',
            'answers': {
                0: {'answer_index': 1, 'answer_text': 'Business'},  # Business (index 1)
                1: {'answer_index': 2, 'answer_text': 'មធ្យម'},  # Average grades
                2: {'answer_index': 1, 'answer_text': 'សៀមរាប'},  # Siem Reap
                3: {'answer_index': 0, 'answer_text': '<$500'},  # Low budget
                4: {'answer_index': 0, 'answer_text': 'សាធារណៈ'},  # Public uni
                5: {'answer_index': 1, 'answer_text': 'ស្ថិរភាព'},  # Stability
                6: {'answer_index': 0, 'answer_text': 'មិនបាន'},  # No experience
                7: {'answer_index': 1, 'answer_text': 'មធ្យម'},  # Medium importance
                8: {'answer_index': 0, 'answer_text': 'ចាំបាច់'},  # Important
                9: {'answer_index': 1, 'answer_text': 'អនឡាញ'}  # Online learning
            }
        },
        {
            'name': 'Creative Arts Student',
            'answers': {
                0: {'answer_index': 3, 'answer_text': 'Arts'},  # Arts (index 3)
                1: {'answer_index': 1, 'answer_text': 'ល្អ'},  # Good grades
                2: {'answer_index': 0, 'answer_text': 'ភ្នំពេញ'},  # Phnom Penh
                3: {'answer_index': 1, 'answer_text': '$500-$1000'},  # Low-mid budget
                4: {'answer_index': 1, 'answer_text': 'ឯកជន'},  # Private uni
                5: {'answer_index': 2, 'answer_text': 'ច្នៃប្រឌិត'},  # Creativity
                6: {'answer_index': 1, 'answer_text': 'បាន'},  # Has experience
                7: {'answer_index': 0, 'answer_text': 'ចាំបាច់'},  # Important
                8: {'answer_index': 1, 'answer_text': 'ចាំបាច់'},  # Important
                9: {'answer_index': 2, 'answer_text': 'ចម្រុះ'}  # Mixed learning
            }
        }
    ]
    
    # Define test majors with different characteristics
    test_majors = [
        {
            'name': 'Perfect CS Match',
            'major_data': {
                'major_name_en': 'Computer Science',
                'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                'city': 'Phnom Penh',
                'tuition_fees_usd': 1500,
                'employment_rate': '90%',
                'founding_year': 1996,
                'field_category': 'technology',
                'university_type': 'private'
            }
        },
        {
            'name': 'Budget-Friendly Business',
            'major_data': {
                'major_name_en': 'Business Administration',
                'major_name_kh': 'រដ្ឋបាលអាជីវកម្ម',
                'city': 'Siem Reap',
                'tuition_fees_usd': 400,
                'employment_rate': '75%',
                'founding_year': 1980,
                'field_category': 'business',
                'university_type': 'public'
            }
        },
        {
            'name': 'Expensive CS Program',
            'major_data': {
                'major_name_en': 'Computer Science',
                'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                'city': 'Phnom Penh',
                'tuition_fees_usd': 4000,  # Very expensive
                'employment_rate': '95%',
                'founding_year': 2000,
                'field_category': 'technology',
                'university_type': 'private'
            }
        },
        {
            'name': 'Arts Program',
            'major_data': {
                'major_name_en': 'Fine Arts',
                'major_name_kh': 'សិល្បៈវិចិត្រ',
                'city': 'Phnom Penh',
                'tuition_fees_usd': 800,
                'employment_rate': '65%',
                'founding_year': 1995,
                'field_category': 'arts',
                'university_type': 'private'
            }
        },
        {
            'name': 'Wrong Location Match',
            'major_data': {
                'major_name_en': 'Computer Science',
                'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                'city': 'Battambang',  # Wrong location for PP student
                'tuition_fees_usd': 1200,
                'employment_rate': '85%',
                'founding_year': 2005,
                'field_category': 'technology',
                'university_type': 'public'
            }
        }
    ]
    
    print('📊 COMPREHENSIVE SCORING RESULTS:')
    print('-' * 50)
    
    total_tests = 0
    accurate_scores = 0
    
    for persona in test_personas:
        print(f'\n👤 {persona["name"]}:')
        print('-' * 30)
        
        for major in test_majors:
            score = calculate_mcda_score(persona['answers'], major['major_data'])
            total_tests += 1
            
            print(f'  {major["name"]}:')
            print(f'    Major: {major["major_data"]["major_name_en"]}')
            print(f'    Location: {major["major_data"]["city"]}')
            print(f'    Fees: ${major["major_data"]["tuition_fees_usd"]}')
            print(f'    Score: {score:.3f} ({score*100:.1f}%)')
            
            # Evaluate accuracy based on expected outcomes
            is_accurate = evaluate_score_accuracy(persona, major, score)
            if is_accurate:
                accurate_scores += 1
                print(f'    ✅ ACCURATE')
            else:
                print(f'    ❌ NEEDS IMPROVEMENT')
            print()
    
    accuracy_percentage = (accurate_scores / total_tests) * 100
    print(f'🎯 COMPREHENSIVE ACCURACY: {accurate_scores}/{total_tests} ({accuracy_percentage:.1f}%)')
    
    if accuracy_percentage >= 80:
        print('✅ EXCELLENT ACCURACY - MCDA system is performing well!')
    elif accuracy_percentage >= 60:
        print('⚠️  GOOD ACCURACY - Some improvements recommended')
    else:
        print('❌ NEEDS SIGNIFICANT IMPROVEMENT')
    
    # Provide specific recommendations
    print('\n📈 DETAILED IMPROVEMENT RECOMMENDATIONS:')
    if accuracy_percentage < 80:
        print('• Enhance field matching logic for better interest alignment')
        print('• Improve budget scoring to better penalize mismatches')
        print('• Add location preference weighting adjustments')
        print('• Include university reputation and ranking factors')
        print('• Consider employment rate impact on scoring')
        print('• Add bonus for perfect multi-criteria matches')
    else:
        print('• System is performing well, consider fine-tuning edge cases')
        print('• Add more sophisticated career goal matching')
        print('• Include program-specific quality metrics')


def evaluate_score_accuracy(persona, major, score):
    """Evaluate if the MCDA score is accurate for the given persona and major."""
    persona_name = persona['name']
    major_name = major['name']
    
    # Define expected score ranges based on persona-major combinations
    if persona_name == 'Tech-Savvy Student (PP)':
        if major_name == 'Perfect CS Match':
            return score >= 0.75  # Should be high
        elif major_name == 'Expensive CS Program':
            return 0.5 <= score <= 0.75  # Good field but budget penalty
        elif major_name == 'Wrong Location Match':
            return 0.4 <= score <= 0.7  # Good field but location penalty
        else:
            return score <= 0.5  # Poor matches
    
    elif persona_name == 'Budget-Conscious Student (SR)':
        if major_name == 'Budget-Friendly Business':
            return score >= 0.7  # Perfect match
        elif major_name == 'Perfect CS Match':
            return 0.3 <= score <= 0.6  # Wrong field, wrong location
        else:
            return score <= 0.5  # Poor matches
    
    elif persona_name == 'Creative Arts Student':
        if major_name == 'Arts Program':
            return score >= 0.7  # Good match
        elif major_name == 'Perfect CS Match':
            return 0.3 <= score <= 0.6  # Wrong field
        else:
            return score <= 0.6  # Various mismatches
    
    return True  # Default to accurate if no specific rule


if __name__ == "__main__":
    test_comprehensive_mcda_accuracy()
