#!/usr/bin/env python3
"""
Extract all university information from the dataset for verification
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any

def extract_all_universities() -> List[Dict[str, Any]]:
    """Extract all university information from JSON files"""
    universities = []
    data_dir = Path("data/raw")
    
    print(f"Scanning directory: {data_dir}")
    
    # Search through all JSON files
    for file_path in data_dir.rglob("*.json"):
        try:
            print(f"Processing: {file_path}")
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # Handle both list and dict formats
            items = data if isinstance(data, list) else [data]
            
            for item in items:
                # Check if this is a university object
                if "university" in item:
                    university = item["university"]
                    
                    # Extract basic info
                    uni_info = {
                        'file_path': str(file_path),
                        'id': university.get('id', ''),
                        'name_kh': university.get('name_kh', ''),
                        'name_en': university.get('name_en', university.get('name', '')),
                        'website': university.get('website', ''),
                        'founding_year': university.get('founding_year', ''),
                        'type': university.get('type', ''),
                        'city': university.get('location', {}).get('city', ''),
                        'address_kh': university.get('location', {}).get('address_kh', ''),
                        'address_en': university.get('location', {}).get('address_en', ''),
                        'phone': university.get('contact', {}).get('phone', []),
                        'email': university.get('contact', {}).get('email', ''),
                        'facebook': university.get('contact', {}).get('social_media', {}).get('facebook', ''),
                        'affiliations': university.get('affiliations', [])
                    }
                    
                    universities.append(uni_info)
                    
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue
    
    return universities

def main():
    """Main function to extract and display university information"""
    universities = extract_all_universities()
    
    print(f"\n=== FOUND {len(universities)} UNIVERSITIES ===\n")
    
    for i, uni in enumerate(universities, 1):
        print(f"{i}. {uni['name_en']} ({uni['name_kh']})")
        print(f"   ID: {uni['id']}")
        print(f"   Type: {uni['type']}")
        print(f"   Founded: {uni['founding_year']}")
        print(f"   City: {uni['city']}")
        print(f"   Website: {uni['website']}")
        print(f"   Phone: {uni['phone']}")
        print(f"   Email: {uni['email']}")
        print(f"   Facebook: {uni['facebook']}")
        print(f"   File: {uni['file_path']}")
        print("-" * 80)
    
    # Save to file for reference
    output_file = "tools/university_list.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(universities, f, ensure_ascii=False, indent=2)
    
    print(f"\nUniversity list saved to: {output_file}")

if __name__ == "__main__":
    main()
