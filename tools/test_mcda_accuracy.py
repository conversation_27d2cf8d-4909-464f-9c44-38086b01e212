#!/usr/bin/env python3
"""
Test MCDA Scoring Accuracy
Tests the current MCDA system and identifies areas for improvement.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.core.mcda.scorer import calculate_mcda_score

def test_mcda_accuracy():
    """Test MCDA scoring accuracy with various scenarios."""
    print('🔍 TESTING CURRENT MCDA SCORING ACCURACY')
    print('=' * 50)
    
    # Test with sample user answers for STEM/Computer Science interest
    test_answers = {
        0: {'answer_index': 0, 'answer_text': 'STEM'},  # STEM interest (includes CS)
        1: {'answer_index': 1, 'answer_text': 'ល្អ'},  # Good grades
        2: {'answer_index': 0, 'answer_text': 'ភ្នំពេញ'},  # Phnom Penh location
        3: {'answer_index': 2, 'answer_text': '$1000-$2000'},  # Budget
        4: {'answer_index': 1, 'answer_text': 'ឯកជន'},  # Private university
        5: {'answer_index': 0, 'answer_text': 'ប្រាក់ខែខ្ពស់'},  # High salary career
        6: {'answer_index': 1, 'answer_text': 'បាន'},  # Has experience
        7: {'answer_index': 0, 'answer_text': 'ចាំបាច់'},  # Important
        8: {'answer_index': 1, 'answer_text': 'ចាំបាច់'},  # Important
        9: {'answer_index': 0, 'answer_text': 'រៀនផ្ទាល់ខ្លួន'}  # In-person learning
    }
    
    # Test cases: Perfect match vs Poor match
    test_cases = [
        {
            'name': 'Perfect CS Match',
            'major_data': {
                'major_name_en': 'Computer Science',
                'city': 'Phnom Penh',
                'tuition_fees_usd': 1500,
                'employment_rate': '90%',
                'founding_year': 1996
            }
        },
        {
            'name': 'Good CS Match',
            'major_data': {
                'major_name_en': 'Information Technology',
                'city': 'Phnom Penh',
                'tuition_fees_usd': 1200,
                'employment_rate': '85%',
                'founding_year': 2000
            }
        },
        {
            'name': 'Poor Match (Wrong Field)',
            'major_data': {
                'major_name_en': 'Literature',
                'city': 'Siem Reap',
                'tuition_fees_usd': 3000,
                'employment_rate': '60%',
                'founding_year': 2010
            }
        },
        {
            'name': 'Budget Mismatch',
            'major_data': {
                'major_name_en': 'Computer Science',
                'city': 'Phnom Penh',
                'tuition_fees_usd': 5000,  # Way over budget
                'employment_rate': '90%',
                'founding_year': 1996
            }
        }
    ]
    
    print('📊 MCDA SCORING RESULTS:')
    print('-' * 40)
    
    accurate_scores = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        score = calculate_mcda_score(test_answers, test_case['major_data'])
        print(f'{i}. {test_case["name"]}:')
        print(f'   Major: {test_case["major_data"]["major_name_en"]}')
        print(f'   Location: {test_case["major_data"]["city"]}')
        print(f'   Fees: ${test_case["major_data"]["tuition_fees_usd"]}')
        print(f'   MCDA Score: {score:.3f} ({score*100:.1f}%)')
        
        # Evaluate accuracy with updated criteria
        is_accurate = False
        if test_case['name'] == 'Perfect CS Match' and score >= 0.8:
            print(f'   ✅ EXCELLENT - High score for perfect match')
            is_accurate = True
        elif test_case['name'] == 'Good CS Match' and score >= 0.8:
            print(f'   ✅ GOOD - Appropriate score for good match')
            is_accurate = True
        elif test_case['name'] == 'Poor Match (Wrong Field)' and score <= 0.5:
            print(f'   ✅ CORRECT - Low score for poor match')
            is_accurate = True
        elif test_case['name'] == 'Budget Mismatch' and score <= 0.75:
            print(f'   ✅ CORRECT - Penalized for budget mismatch')
            is_accurate = True
        else:
            print(f'   ❌ NEEDS IMPROVEMENT - Score not appropriate')
        
        if is_accurate:
            accurate_scores += 1
        print()
    
    accuracy_percentage = (accurate_scores / total_tests) * 100
    print(f'🎯 OVERALL MCDA ACCURACY: {accurate_scores}/{total_tests} ({accuracy_percentage:.1f}%)')
    
    if accuracy_percentage >= 80:
        print('✅ EXCELLENT ACCURACY - MCDA system is working well!')
    elif accuracy_percentage >= 60:
        print('⚠️  GOOD ACCURACY - Some improvements needed')
    else:
        print('❌ NEEDS SIGNIFICANT IMPROVEMENT')
    
    print('\n📈 IMPROVEMENT RECOMMENDATIONS:')
    print('• Better field matching accuracy')
    print('• More sophisticated budget scoring')
    print('• Enhanced location preferences')
    print('• Improved career goal alignment')
    print('• Add university reputation factors')
    print('• Include employment rate weighting')

if __name__ == "__main__":
    test_mcda_accuracy()
