#!/usr/bin/env python3
"""
Count Actual Universities Tool
Identifies unique universities (not duplicate files for same university).
"""

import json
import sys
from pathlib import Path
from typing import Dict, Set

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def get_unique_universities_by_city() -> Dict[str, Dict]:
    """Get unique universities by city, avoiding duplicates."""
    data_dir = project_root / "data" / "raw"
    
    cities = {
        'PP': 'Phnom Penh',
        'SR': 'Siem Reap', 
        'BTB': 'Battambang'
    }
    
    results = {}
    
    for city_code, city_name in cities.items():
        city_dir = data_dir / city_code
        if not city_dir.exists():
            continue
            
        unique_unis = {}
        json_files = list(city_dir.glob("*.json"))
        
        print(f"\n🔍 Analyzing {city_name} ({len(json_files)} files)...")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Extract university identifier
                uni_id = None
                uni_name = None
                
                if 'university' in data and 'id' in data['university']:
                    uni_id = data['university']['id']
                    uni_name = data['university'].get('name', 'Unknown')
                elif 'university' in data and 'name' in data['university']:
                    uni_name = data['university']['name']
                    uni_id = json_file.stem
                else:
                    # Try to extract from programmes
                    if 'programmes' in data and len(data['programmes']) > 0:
                        uni_name = data['programmes'][0].get('university_name', json_file.stem)
                        uni_id = json_file.stem
                    else:
                        uni_name = json_file.stem
                        uni_id = json_file.stem
                
                # Group by university ID/name to avoid duplicates
                if uni_id:
                    if uni_id not in unique_unis:
                        unique_unis[uni_id] = {
                            'name': uni_name,
                            'files': [],
                            'id': uni_id
                        }
                    unique_unis[uni_id]['files'].append(str(json_file))
                
            except Exception as e:
                print(f"❌ Error reading {json_file}: {e}")
        
        # Print details for this city
        print(f"📊 Unique universities in {city_name}: {len(unique_unis)}")
        for uni_id, info in sorted(unique_unis.items()):
            file_count = len(info['files'])
            if file_count > 1:
                print(f"   • {info['name']} ({file_count} files)")
            else:
                print(f"   • {info['name']}")
        
        results[city_code] = {
            'city_name': city_name,
            'unique_universities': unique_unis,
            'total_files': len(json_files),
            'unique_count': len(unique_unis)
        }
    
    return results

def generate_accurate_summary(results: Dict) -> str:
    """Generate accurate university summary."""
    
    summary = "# Accurate University Count in EduGuideBot Data\n\n"
    
    total_unique = 0
    total_files = 0
    
    for city_code, city_info in results.items():
        city_name = city_info['city_name']
        unique_count = city_info['unique_count']
        file_count = city_info['total_files']
        
        total_unique += unique_count
        total_files += file_count
        
        summary += f"## {city_name}\n"
        summary += f"- **Unique Universities**: {unique_count}\n"
        summary += f"- **Total Files**: {file_count}\n\n"
        
        summary += "### Universities:\n"
        for uni_id, info in sorted(city_info['unique_universities'].items()):
            file_count = len(info['files'])
            if file_count > 1:
                summary += f"- **{info['name']}** ({file_count} files - different majors/departments)\n"
            else:
                summary += f"- **{info['name']}**\n"
        summary += "\n"
    
    summary += f"## Summary\n"
    summary += f"- **Total Unique Universities**: {total_unique}\n"
    summary += f"- **Total Data Files**: {total_files}\n"
    summary += f"- **Average Files per University**: {total_files/total_unique:.1f}\n\n"
    
    summary += "**Note**: Some universities have multiple files because they contain different majors or departments.\n"
    
    return summary

def main():
    """Main counting process."""
    print("🚀 COUNTING ACTUAL UNIQUE UNIVERSITIES")
    print("=" * 60)
    
    # Get unique universities by city
    results = get_unique_universities_by_city()
    
    # Generate accurate summary
    summary = generate_accurate_summary(results)
    summary_path = project_root / "build" / "accurate_university_count.md"
    summary_path.parent.mkdir(exist_ok=True)
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(f"\n📄 Accurate count saved to: {summary_path}")
    print("\n" + "="*60)
    print("FINAL ACCURATE COUNT:")
    print("="*60)
    
    total_unique = sum(city_info['unique_count'] for city_info in results.values())
    
    for city_code, city_info in results.items():
        print(f"{city_info['city_name']}: {city_info['unique_count']} unique universities")
    
    print(f"TOTAL: {total_unique} unique universities")
    print("="*60)

if __name__ == "__main__":
    main()
