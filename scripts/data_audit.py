#!/usr/bin/env python3
"""
Comprehensive Data Audit System for EduGuideBot v3
Enhanced data quality validation with detailed reporting and quality scoring
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict
from datetime import datetime


def validate_record(data: Dict[str, Any], file_path: str) -> List[Tuple[str, str, str]]:
    """
    Validate a single university record for mandatory fields
    
    Args:
        data: University data dictionary
        file_path: Path to the file being validated
        
    Returns:
        List of (field_name, issue_type, details) tuples
    """
    issues = []
    
    # Check university-level mandatory fields
    if 'university' not in data:
        issues.append(('university', 'missing_section', 'No university section found'))
        return issues
    
    university = data['university']
    
    # university.id
    if 'id' not in university or not university['id']:
        issues.append(('university.id', 'missing', 'University ID is missing or empty'))
    
    # name_kh
    if 'name_kh' not in university or not university['name_kh']:
        issues.append(('university.name_kh', 'missing', 'Khmer name is missing or empty'))
    
    # location.city
    if 'location' not in university:
        issues.append(('university.location', 'missing_section', 'Location section missing'))
    elif 'city' not in university['location'] or not university['location']['city']:
        issues.append(('university.location.city', 'missing', 'City is missing or empty'))
    
    # Check programmes
    if 'programmes' not in data:
        issues.append(('programmes', 'missing_section', 'No programmes section found'))
        return issues
    
    programmes = data['programmes']
    if not isinstance(programmes, list):
        issues.append(('programmes', 'invalid_type', 'Programmes should be a list'))
        return issues
    
    for i, programme in enumerate(programmes):
        prog_prefix = f'programmes[{i}]'
        
        # major_info.id
        if 'major_info' not in programme:
            issues.append((f'{prog_prefix}.major_info', 'missing_section', 'Major info section missing'))
        elif 'id' not in programme['major_info'] or not programme['major_info']['id']:
            issues.append((f'{prog_prefix}.major_info.id', 'missing', 'Programme ID is missing or empty'))
        
        # tuition_fees_usd
        if 'tuition_fees_usd' not in programme:
            issues.append((f'{prog_prefix}.tuition_fees_usd', 'missing', 'Tuition fees missing'))
        elif programme['tuition_fees_usd'] in [None, '', 0, '0']:
            issues.append((f'{prog_prefix}.tuition_fees_usd', 'invalid', 'Tuition fees is null/zero/empty'))
        
        # employment_rate
        if 'employment_rate' not in programme:
            issues.append((f'{prog_prefix}.employment_rate', 'missing', 'Employment rate missing'))
        elif programme['employment_rate'] in [None, '', 0, '0', '0%']:
            issues.append((f'{prog_prefix}.employment_rate', 'invalid', 'Employment rate is null/zero/empty'))
        
        # faculty_information.student_faculty_ratio
        if 'faculty_information' not in programme:
            issues.append((f'{prog_prefix}.faculty_information', 'missing_section', 'Faculty info section missing'))
        elif 'student_faculty_ratio' not in programme['faculty_information']:
            issues.append((f'{prog_prefix}.faculty_information.student_faculty_ratio', 'missing', 'Student-faculty ratio missing'))
        elif not programme['faculty_information']['student_faculty_ratio']:
            issues.append((f'{prog_prefix}.faculty_information.student_faculty_ratio', 'invalid', 'Student-faculty ratio is empty'))
    
    return issues


def audit_data_files(data_dir: Path) -> Tuple[Dict[str, int], List[Tuple[str, str, str, str]]]:
    """
    Audit all JSON files in data directory
    
    Args:
        data_dir: Path to data directory
        
    Returns:
        Tuple of (field_issue_counts, detailed_issues)
    """
    field_issues = defaultdict(int)
    detailed_issues = []
    
    # Find all JSON files
    json_files = list(data_dir.glob('**/*.json'))
    print(f"Found {len(json_files)} JSON files to audit")
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Validate this file
            issues = validate_record(data, str(json_file))
            
            for field_name, issue_type, details in issues:
                field_issues[field_name] += 1
                detailed_issues.append((str(json_file), field_name, issue_type, details))
                
        except json.JSONDecodeError as e:
            field_issues['_json_parse_error'] += 1
            detailed_issues.append((str(json_file), '_json_parse_error', 'parse_error', f'JSON parse error: {e}'))
        except Exception as e:
            field_issues['_file_read_error'] += 1
            detailed_issues.append((str(json_file), '_file_read_error', 'read_error', f'File read error: {e}'))
    
    return dict(field_issues), detailed_issues


def generate_audit_report(field_issues: Dict[str, int], detailed_issues: List[Tuple[str, str, str, str]], output_path: str) -> None:
    """
    Generate markdown audit report
    
    Args:
        field_issues: Dictionary of field -> issue count
        detailed_issues: List of detailed issue tuples
        output_path: Path to save report
    """
    total_files = len(set(issue[0] for issue in detailed_issues))
    total_issues = sum(field_issues.values())
    
    # Create report content
    report = f"""# Data Quality Audit Report

## Summary

- **Total files audited**: {total_files}
- **Total issues found**: {total_issues}
- **Files with issues**: {len(set(issue[0] for issue in detailed_issues if issue[1] != '_json_parse_error'))}

## Issues by Field

| Field | Issue Count | % of Files |
|-------|-------------|------------|
"""
    
    # Sort fields by issue count (descending)
    for field, count in sorted(field_issues.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_files * 100) if total_files > 0 else 0
        report += f"| {field} | {count} | {percentage:.1f}% |\n"
    
    # Top 20 offending files
    report += f"\n## Top 20 Offending Files\n\n"
    
    # Group issues by file
    file_issue_counts = defaultdict(int)
    for file_path, _, _, _ in detailed_issues:
        file_issue_counts[file_path] += 1
    
    # Sort files by issue count
    top_files = sorted(file_issue_counts.items(), key=lambda x: x[1], reverse=True)[:20]
    
    report += "| File | Issues | Details |\n"
    report += "|------|--------|----------|\n"
    
    for file_path, issue_count in top_files:
        # Get first few issues for this file
        file_issues = [issue for issue in detailed_issues if issue[0] == file_path][:3]
        details = "; ".join([f"{issue[1]}: {issue[3]}" for issue in file_issues])
        if len(file_issues) > 3:
            details += f" (+{issue_count - 3} more)"
        
        # Shorten file path for display
        short_path = str(Path(file_path).name)
        report += f"| {short_path} | {issue_count} | {details} |\n"
    
    # Critical issues section
    critical_fields = [
        'university.id', 'university.name_kh', 'university.location.city',
        'programmes[].major_info.id', 'programmes[].tuition_fees_usd', 
        'programmes[].employment_rate', 'programmes[].faculty_information.student_faculty_ratio'
    ]
    
    critical_issues = {field: count for field, count in field_issues.items() 
                      if any(critical in field for critical in critical_fields)}
    
    if critical_issues:
        report += f"\n## Critical Issues (Blocking)\n\n"
        report += "These issues will prevent proper scoring and display:\n\n"
        for field, count in sorted(critical_issues.items(), key=lambda x: x[1], reverse=True):
            report += f"- **{field}**: {count} issues\n"
    
    # Save report
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"Audit report saved to {output_path}")


def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Audit data quality of university records')
    parser.add_argument('--data-dir', default='data/raw', help='Data directory to audit')
    parser.add_argument('--output', default='build/data_audit_report.md', help='Output report file')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    if not data_dir.exists():
        print(f"Error: Data directory not found: {data_dir}")
        sys.exit(1)
    
    print("Starting data quality audit...")
    
    # Run audit
    field_issues, detailed_issues = audit_data_files(data_dir)
    
    # Generate report
    generate_audit_report(field_issues, detailed_issues, args.output)
    
    # Check for critical issues
    total_issues = sum(field_issues.values())
    if total_issues > 0:
        print(f"\n❌ AUDIT FAILED: {total_issues} issues found")
        print("Run auto-patch to fix critical gaps")
        sys.exit(1)
    else:
        print("\n✅ AUDIT PASSED: No issues found")
        sys.exit(0)


if __name__ == '__main__':
    main()
