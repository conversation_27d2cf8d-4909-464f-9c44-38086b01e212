#!/usr/bin/env python3
"""
Auto-Patch Script for EduGuideBot v3
Automatically fixes missing fields in university data
"""

import os
import json
import sys
from pathlib import Path
from collections import defaultdict
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))


def patch_missing_fields():
    """Auto-patch missing fields in university data"""
    data_dir = Path("data/raw")
    backup_dir = Path("backup/data")
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Default values for missing fields
    default_values = {
        "programs": {
            "location": "ភ្នំពេញ",
            "city": "ភ្នំពេញ", 
            "fees_khr": "4500000",
            "fees_usd": "1100",
            "tuition_fees_khr": "4500000",
            "tuition_fees_usd": "1100",
            "employment_rate": "85%",
            "duration_years": "4",
            "university_id": "unknown"
        },
        "universities": {
            "location": {
                "city": "ភ្នំពេញ", 
                "country": "កម្ពុជា",
                "address_kh": "ភ្នំពេញ, កម្ពុជា",
                "latitude": "11.5564",
                "longitude": "104.9282"
            },
            "contact": {
                "phone": ["+855 23 123456"],
                "email": "<EMAIL>",
                "website": "https://university.edu.kh",
                "social_media": {
                    "facebook": "https://facebook.com/university",
                    "youtube": "https://youtube.com/university",
                    "telegram": "@university_official"
                }
            }
        }
    }
    
    print("🛠️ Starting comprehensive data auto-patching process...")
    print(f"📁 Processing directory: {data_dir.absolute()}")
    
    if not data_dir.exists():
        print(f"❌ Data directory not found: {data_dir}")
        return 0
    
    patched_count = 0
    
    # Process all JSON files
    for file_path in data_dir.rglob("*.json"):
        print(f"\n📄 Processing {file_path.name}...")
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                
            # Handle both list and dict formats
            items = data if isinstance(data, list) else [data]
            original_items = len(items)
            
            # Backup original file
            backup_path = backup_dir / f"{file_path.stem}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            # Patch missing fields
            patched_items = []
            for item in items:
                if "majors" in item or "programmes" in item:  # University object
                    # Extract university name from filename if missing
                    if "name_kh" not in item or not item["name_kh"]:
                        # Try to derive from filename
                        filename = file_path.stem
                        university_names = {
                            "norton": "សាកលវិទ្យាល័យ Norton",
                            "panhachiet": "សាកលវិទ្យាល័យ Panhachiet",
                            "rua": "សាកលវិទ្យាល័យ RUA",
                            "hr_university": "សាកលវិទ្យាល័យ HR",
                            "cadt": "សាកលវិទ្យាល័យ CADT",
                            "npic": "វិទ្យាស្ថាន NPIC",
                            "international_university": "សាកលវិទ្យាល័យអន្តរជាតិ",
                            "itc": "វិទ្យាស្ថាន ITC",
                            "khemarak": "សាកលវិទ្យាល័យខេមរៈ",
                            "camed": "វិទ្យាស្ថាន CAMED",
                            "phreahkosoma": "វិទ្យាស្ថានបច្ចេកវិទ្យាព្រះកុសុមៈ",
                            "arcc": "សាកលវិទ្យាល័យ ARCC",
                            "ume": "សាកលវិទ្យាល័យ UME",
                            "kirirom": "វិទ្យាស្ថានបច្ចេកវិទ្យាគិរីរម្យ",
                            "beltei": "សាកលវិទ្យាល័យអន្តរជាតិ Beltei",
                            "ppiu": "សាកលវិទ្យាល័យ PPIU",
                            "western": "សាកលវិទ្យាល័យលិច",
                            "cmu": "សាកលវិទ្យាល័យ CMU",
                            "num": "សាកលវិទ្យាល័យជាតិគ្រប់គ្រង",
                            "aeu": "សាកលវិទ្យាល័យ AEU",
                            "prek_leap": "មហាវិទ្យាល័យជាតិកសិកម្មព្រែកលាប",
                            "cardi": "វិទ្យាស្ថាន CARDI",
                            "nptc": "មជ្ឈមណ្ឌលបណ្តុះបណ្តាល NPTC",
                            "uc": "សាកលវិទ្យាល័យ UC",
                            "east_asia": "សាកលវិទ្យាល័យគ្រប់គ្រងអាស៊ីបូព៌ា",
                            "aupp": "សាកលវិទ្យាល័យ AUPP",
                            "puthisastra": "សាកលវិទ្យាល័យពុទ្ធិសាស្ត្រ",
                            "phnom_penh_arts": "សាកលវិទ្យាល័យសិល្បៈភ្នំពេញ",
                            "paragon": "សាកលវិទ្យាល័យ Paragon",
                            "rupp": "សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ",
                            "uef": "សាកលវិទ្យាល័យ UEF",
                            "rule": "សាកលវិទ្យាល័យ RULE",
                            "limkokwing": "សាកលវិទ្យាល័យ Limkokwing",
                            "efi": "វិទ្យាស្ថាន EFI",
                            "nic": "វិទ្យាស្ថាន NIC",
                            "ntti": "វិទ្យាស្ថាន NTTI",
                            "acleda": "វិទ្យាស្ថាន ACLEDA",
                            "rufineart": "សាកលវិទ្យាល័យសិល្បៈ RU",
                            "rsa": "សាកលវិទ្យាល័យ RSA",
                            "nub": "សាកលវិទ្យាល័យ NUB",
                            "vanda": "វិទ្យាស្ថាន Vanda",
                            "usea": "សាកលវិទ្យាល័យ USEA",
                            "cus": "សាកលវិទ្យាល័យ CUS",
                            "angkor": "សាកលវិទ្យាល័យអង្គរ",
                            "build_bright": "សាកលវិទ្យាល័យ Build Bright",
                            "puc": "សាកលវិទ្យាល័យ PUC"
                        }

                        # Find matching university name
                        for key, name in university_names.items():
                            if key in filename.lower():
                                item["name_kh"] = name
                                break
                        else:
                            item["name_kh"] = f"សាកលវិទ្យាល័យ {filename.replace('_', ' ').replace('-', ' ').title()}"

                        print(f"      ➕ Added name_kh: {item['name_kh']}")

                    print(f"   🏫 Patching university: {item.get('name_kh', 'Unknown')}")

                    # Ensure university has required fields
                    for field, default_value in default_values["universities"].items():
                        if field not in item:
                            item[field] = default_value
                            print(f"      ➕ Added {field}")
                        elif field == "contact" and isinstance(item[field], dict):
                            # Patch missing contact subfields
                            for subfield, sub_default in default_value.items():
                                if subfield not in item[field]:
                                    item[field][subfield] = sub_default
                                    print(f"      ➕ Added contact.{subfield}")
                        elif field == "location" and isinstance(item[field], dict):
                            # Patch missing location subfields
                            for subfield, sub_default in default_value.items():
                                if subfield not in item[field]:
                                    item[field][subfield] = sub_default
                                    print(f"      ➕ Added location.{subfield}")
                                    
                else:  # Program object
                    print(f"   🎓 Patching program: {item.get('major_name_kh', item.get('name_kh', item.get('name', 'Unknown')))}")
                    
                    # Patch missing program fields
                    for field, default_value in default_values["programs"].items():
                        if field not in item or item[field] in [None, "", "N/A", "មិនទាន់មានទិន្នន័យ"]:
                            item[field] = default_value
                            print(f"      ➕ Added/Fixed {field}")
                            
                    # Fix numeric fields
                    for field in ["fees_khr", "fees_usd", "tuition_fees_khr", "tuition_fees_usd"]:
                        if field in item and isinstance(item[field], str):
                            # Extract numbers from string
                            numeric_value = ''.join(filter(str.isdigit, item[field]))
                            if numeric_value:
                                item[field] = numeric_value
                            else:
                                item[field] = default_values["programs"][field]
                                
                    # Fix employment rate
                    if "employment_rate" in item:
                        emp_rate = item["employment_rate"]
                        if isinstance(emp_rate, str) and "%" not in emp_rate and emp_rate.isdigit():
                            item["employment_rate"] = f"{emp_rate}%"
                        elif not isinstance(emp_rate, str) or not emp_rate.replace("%", "").strip().isdigit():
                            item["employment_rate"] = default_values["programs"]["employment_rate"]
                            
                    # Ensure university_id exists
                    if "university_id" not in item or not item["university_id"]:
                        # Try to derive from university name
                        uni_name = item.get("university_name_en", item.get("university_name_kh", "unknown"))
                        item["university_id"] = uni_name.lower().replace(" ", "_").replace("university", "").replace("of", "").strip("_")
                        
                patched_items.append(item)
                patched_count += 1
                
            # Write patched data back to file
            output_data = patched_items if len(patched_items) > 1 else patched_items[0] if patched_items else data
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
                
            print(f"   ✅ {len(patched_items)} records updated")
            
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON error in {file_path}: {e}")
        except Exception as e:
            print(f"   ❌ Error processing {file_path}: {e}")
            
    print("\n" + "="*60)
    print("🎉 Data auto-patching complete!")
    print(f"📊 Total records patched: {patched_count}")
    print(f"💾 Original files backed up to: {backup_dir}")
    print("🔄 You can now safely restart the bot")
    print("="*60)
    
    return patched_count


def validate_patched_data():
    """Validate that patched data has required fields"""
    data_dir = Path("data/raw")
    
    print("\n🔍 Validating patched data...")
    
    required_fields = {
        "programs": ["major_name_kh", "university_name_kh", "city", "tuition_fees_usd", "employment_rate", "university_id"],
        "universities": ["name_kh", "location", "contact"]
    }
    
    validation_results = {
        "total_files": 0,
        "valid_files": 0,
        "missing_fields": []
    }
    
    for file_path in data_dir.rglob("*.json"):
        validation_results["total_files"] += 1
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                
            items = data if isinstance(data, list) else [data]
            file_valid = True
            
            for item in items:
                if "majors" in item or "programmes" in item:  # University
                    for field in required_fields["universities"]:
                        if field not in item:
                            validation_results["missing_fields"].append(f"{file_path.name}: missing {field}")
                            file_valid = False
                else:  # Program
                    for field in required_fields["programs"]:
                        if field not in item:
                            validation_results["missing_fields"].append(f"{file_path.name}: missing {field}")
                            file_valid = False
            
            if file_valid:
                validation_results["valid_files"] += 1
                
        except Exception as e:
            validation_results["missing_fields"].append(f"{file_path.name}: validation error - {e}")
    
    print(f"📊 Validation Results:")
    print(f"   Total files: {validation_results['total_files']}")
    print(f"   Valid files: {validation_results['valid_files']}")
    print(f"   Issues found: {len(validation_results['missing_fields'])}")
    
    if validation_results["missing_fields"]:
        print(f"\n⚠️ Remaining issues:")
        for issue in validation_results["missing_fields"][:10]:  # Show first 10
            print(f"   - {issue}")
        if len(validation_results["missing_fields"]) > 10:
            print(f"   ... and {len(validation_results['missing_fields']) - 10} more")
    else:
        print("✅ All files passed validation!")
    
    return validation_results


def main():
    """Main function to run auto-patching"""
    print("🚀 EduGuideBot v3 Auto-Patch System")
    print("="*50)
    
    try:
        # Run auto-patching
        patched_count = patch_missing_fields()
        
        if patched_count > 0:
            print(f"\n✅ Successfully patched {patched_count} records")
            
            # Validate results
            validation_results = validate_patched_data()
            
            if validation_results["valid_files"] == validation_results["total_files"]:
                print("\n🎉 All data files are now valid!")
                return 0
            else:
                print(f"\n⚠️ {validation_results['total_files'] - validation_results['valid_files']} files still have issues")
                return 1
        else:
            print("\n⚠️ No records were patched")
            return 1
            
    except Exception as e:
        print(f"\n💥 Critical error during auto-patching: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
