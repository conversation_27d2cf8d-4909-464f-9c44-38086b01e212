#!/usr/bin/env python3
"""
Daily Report Generator for EduGuideBot v3
Generates comprehensive daily health check reports
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from bot.app import create_bot_application
from tools.ux_simulator import EduGuideBotSimulator
import nest_asyncio

# Apply async patch for macOS/Linux environments
nest_asyncio.apply()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def generate_daily_report():
    """Generate comprehensive daily health check report."""
    report = {
        "timestamp": datetime.now().isoformat(),
        "date": datetime.now().strftime("%Y-%m-%d"),
        "total_handlers": 0,
        "command_handlers": 0,
        "callback_handlers": 0,
        "bot_startup": False,
        "ux_simulation_passed": False,
        "data_loading": False,
        "hybrid_scoring": False,
        "errors_found": [],
        "warnings": [],
        "recommendations": []
    }

    print("📊 Generating EduGuideBot v3 Daily Report...")
    print("=" * 60)

    # Test 1: Bot Application Creation
    print("1️⃣ Testing bot application creation...")
    try:
        bot_token = "**********:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y"
        application = create_bot_application(bot_token)
        await application.initialize()
        
        # Count handlers
        report["total_handlers"] = sum(len(handlers) for handlers in application.handlers.values())
        
        for group_handlers in application.handlers.values():
            for handler in group_handlers:
                if hasattr(handler, 'command'):
                    report["command_handlers"] += 1
                elif hasattr(handler, 'pattern'):
                    report["callback_handlers"] += 1
        
        report["bot_startup"] = True
        print(f"   ✅ Bot application created successfully")
        print(f"   📊 Total handlers: {report['total_handlers']}")
        print(f"   🎯 Command handlers: {report['command_handlers']}")
        print(f"   🔄 Callback handlers: {report['callback_handlers']}")
        
    except Exception as e:
        report["errors_found"].append(f"Bot startup failed: {str(e)}")
        print(f"   ❌ Bot startup failed: {e}")

    # Test 2: UX Simulation
    print("\n2️⃣ Testing full UX simulation...")
    try:
        simulator = EduGuideBotSimulator(application)
        success = await simulator.run_full_flow()
        report["ux_simulation_passed"] = success
        
        if success:
            print("   ✅ UX simulation passed")
        else:
            print("   ❌ UX simulation failed")
            report["errors_found"].append("UX simulation failed")
            
    except Exception as e:
        report["errors_found"].append(f"UX Simulation failed: {str(e)}")
        print(f"   ❌ UX simulation error: {e}")

    # Test 3: Data Loading
    print("\n3️⃣ Testing data loading...")
    try:
        from core.data_loader import load_raw
        programs = load_raw()
        
        if programs and len(programs) > 0:
            report["data_loading"] = True
            report["total_programs"] = len(programs)
            print(f"   ✅ Data loaded successfully: {len(programs)} programs")
        else:
            report["errors_found"].append("No programs loaded from data files")
            print("   ❌ No programs loaded")
            
    except Exception as e:
        report["errors_found"].append(f"Data loading failed: {str(e)}")
        print(f"   ❌ Data loading error: {e}")

    # Test 4: Hybrid Scoring System
    print("\n4️⃣ Testing hybrid scoring system...")
    try:
        from core.hybrid_recommender import get_recommendations

        # Create test user profile
        test_profile = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},
            1: {'answer_index': 1, 'answer_text': 'Good at math'},
            2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
            3: {'answer_index': 1, 'answer_text': '$500-1000'},
        }

        recommendations = get_recommendations(test_profile, programs[:50])

        if recommendations and len(recommendations) > 0:
            report["hybrid_scoring"] = True
            report["sample_recommendations"] = len(recommendations)
            print(f"   ✅ Hybrid scoring working: {len(recommendations)} recommendations")
        else:
            report["errors_found"].append("Hybrid scoring returned no recommendations")
            print("   ❌ Hybrid scoring failed")

    except Exception as e:
        report["errors_found"].append(f"Hybrid scoring failed: {str(e)}")
        print(f"   ❌ Hybrid scoring error: {e}")

    # Cleanup
    try:
        await application.shutdown()
    except:
        pass

    # Generate recommendations
    if report["errors_found"]:
        report["recommendations"].append("🚨 Critical errors found - immediate attention required")
    
    if report["total_handlers"] < 10:
        report["warnings"].append("⚠️ Low handler count - some features may not work")
    
    if not report["ux_simulation_passed"]:
        report["recommendations"].append("🔧 UX simulation failed - check handler patterns")
    
    if not report["data_loading"]:
        report["recommendations"].append("📊 Data loading issues - check data files")

    # Save report
    build_dir = Path(__file__).parent.parent / "build"
    build_dir.mkdir(exist_ok=True)
    
    report_file = build_dir / "daily_report.json"
    with open(report_file, "w") as f:
        json.dump(report, f, indent=2)

    # Print summary
    print("\n" + "=" * 60)
    print("📋 DAILY REPORT SUMMARY")
    print("=" * 60)
    print(f"📅 Date: {report['date']}")
    print(f"🤖 Bot Startup: {'✅' if report['bot_startup'] else '❌'}")
    print(f"🎯 UX Simulation: {'✅' if report['ux_simulation_passed'] else '❌'}")
    print(f"📊 Data Loading: {'✅' if report['data_loading'] else '❌'}")
    print(f"🧮 Hybrid Scoring: {'✅' if report['hybrid_scoring'] else '❌'}")
    print(f"📈 Total Handlers: {report['total_handlers']}")
    
    if report["errors_found"]:
        print(f"\n🚨 ERRORS FOUND ({len(report['errors_found'])}):")
        for error in report["errors_found"]:
            print(f"   • {error}")
    
    if report["warnings"]:
        print(f"\n⚠️ WARNINGS ({len(report['warnings'])}):")
        for warning in report["warnings"]:
            print(f"   • {warning}")
    
    if report["recommendations"]:
        print(f"\n💡 RECOMMENDATIONS ({len(report['recommendations'])}):")
        for rec in report["recommendations"]:
            print(f"   • {rec}")

    print(f"\n📄 Full report saved to: {report_file}")
    
    # Return overall health status
    overall_health = (
        report["bot_startup"] and 
        report["ux_simulation_passed"] and 
        report["data_loading"] and 
        len(report["errors_found"]) == 0
    )
    
    if overall_health:
        print("\n🎉 EduGuideBot v3 is healthy and ready for production!")
        return True
    else:
        print("\n⚠️ EduGuideBot v3 has issues that need attention.")
        return False


if __name__ == "__main__":
    result = asyncio.run(generate_daily_report())
    
    if result:
        print("\n✅ Daily report completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Daily report found issues!")
        sys.exit(1)
