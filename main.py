#!/usr/bin/env python3
"""
EduGuideBot v3 - University Recommendation Bot for Cambodian Students
Clean rebuild with 16-question assessment and hybrid MCDA+ML recommendation
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Fix for nested event loop issues
try:
    import nest_asyncio
    nest_asyncio.apply()
except ImportError:
    pass

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from bot.app import create_bot_application


def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('bot.log')
        ]
    )

    # Reduce noise from some libraries
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('telegram').setLevel(logging.WARNING)


async def main():
    """Main entry point for EduGuideBot v3."""
    setup_logging()
    logger = logging.getLogger(__name__)

    # Get bot token from environment
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN environment variable not set")
        sys.exit(1)

    logger.info("Starting EduGuideBot v3...")

    try:
        # Create and run the bot application
        application = await create_bot_application(bot_token)
        await application.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        logger.info("EduGuideBot v3 stopped by user")
    except Exception as e:
        logger.error(f"EduGuideBot v3 crashed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
