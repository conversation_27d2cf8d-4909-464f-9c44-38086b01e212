#!/usr/bin/env python3
"""
End-to-end test for the bot recommendations system
"""

import sys
import asyncio
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_bot_recommendations_e2e():
    """Test the complete bot recommendations flow end-to-end"""
    print("🤖 Testing bot recommendations end-to-end...")
    
    try:
        # Import bot modules
        from src.bot.handlers.recommendations import show_recommendations
        from src.bot.ui import create_enhanced_recommendations_view
        from telegram import Update, Message, Chat, User
        from telegram.ext import ContextTypes
        
        # Create mock Telegram objects
        chat = MagicMock(spec=Chat)
        chat.id = 123456789
        chat.type = "private"
        
        user = MagicMock(spec=User)
        user.id = 123456789
        user.first_name = "Test"
        user.username = "testuser"
        
        message = MagicMock(spec=Message)
        message.chat = chat
        message.from_user = user
        message.message_id = 1
        message.text = "/recommend"
        
        update = AsyncMock(spec=Update)
        update.effective_chat = chat
        update.effective_user = user
        update.message = message
        update.callback_query = None
        
        # Create mock context with assessment data
        context = AsyncMock(spec=ContextTypes.DEFAULT_TYPE)
        context.user_data = {
            'assessment': {
                0: {'answer_index': 2, 'answer_text': 'Computer Science'},  # Interest field
                1: {'answer_index': 1, 'answer_text': 'Medium'},            # Budget
                2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},        # Location
                'lang': 'kh'
            }
        }
        
        # Mock the reply methods
        sent_messages = []
        
        async def mock_reply_text(text, reply_markup=None, parse_mode=None):
            sent_messages.append({
                'text': text,
                'reply_markup': reply_markup,
                'parse_mode': parse_mode
            })
            return MagicMock()
            
        message.reply_text = mock_reply_text
        
        print("📊 Testing show_recommendations handler...")
        
        # Call the actual handler
        result = await show_recommendations(update, context)
        
        if result is None:
            print("❌ Handler returned None")
            return False
            
        print(f"✅ Handler returned state: {result}")
        
        # Check if message was sent
        if not sent_messages:
            print("❌ No messages were sent")
            return False
            
        message_data = sent_messages[0]
        print(f"📝 Message sent with {len(message_data['text'])} characters")
        print(f"🔘 Reply markup: {'Yes' if message_data['reply_markup'] else 'No'}")
        
        # Verify message content
        if 'អនុសាសន៍មុខវិជ្ជាសិក្សា' not in message_data['text']:
            print("❌ Message doesn't contain expected Khmer text")
            return False
            
        # Verify buttons
        if not message_data['reply_markup']:
            print("❌ No reply markup found")
            return False
            
        buttons = message_data['reply_markup'].inline_keyboard
        if len(buttons) < 2:
            print(f"❌ Expected at least 2 button rows, got {len(buttons)}")
            return False
            
        print(f"✅ Found {len(buttons)} button rows")
        
        # Test button callback data
        for i, row in enumerate(buttons[:3]):  # Check first 3 rows
            for j, button in enumerate(row):
                callback_data = button.callback_data
                if not callback_data:
                    print(f"❌ Button [{i},{j}] has no callback data")
                    return False
                    
                if len(callback_data.encode('utf-8')) > 64:
                    print(f"❌ Button [{i},{j}] callback data too long: {len(callback_data.encode('utf-8'))} bytes")
                    return False
                    
        print("✅ All button callback data is valid")
        
        # Check if recommendations were cached
        if 'recommendations' not in context.user_data:
            print("❌ Recommendations were not cached in context")
            return False
            
        recommendations = context.user_data['recommendations']
        print(f"✅ {len(recommendations)} recommendations cached")
        
        # Verify recommendation structure
        if recommendations:
            rec = recommendations[0]
            required_fields = ['major_id', 'major_name', 'university_id', 'university_name']
            for field in required_fields:
                if field not in rec:
                    print(f"❌ Missing field '{field}' in recommendation")
                    return False
                    
        print("✅ Recommendation structure is valid")
        
        print("🎉 End-to-end test passed!")
        return True
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_ui_with_real_data():
    """Test UI creation with real recommendation data"""
    print("\n🎨 Testing UI with real data...")
    
    try:
        from src.core.recommender import get_recommendations
        from src.bot.ui import create_enhanced_recommendations_view
        
        # Create sample assessment
        assessment = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},
            1: {'answer_index': 1, 'answer_text': 'Medium'},
            2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
            'lang': 'kh'
        }
        
        # Get real recommendations
        recommendations = await get_recommendations(assessment)
        
        if not recommendations:
            print("❌ No recommendations generated")
            return False
            
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        # Test UI creation in both languages
        for lang in ['kh', 'en']:
            print(f"🌐 Testing {lang} language...")
            
            message_text, reply_markup = create_enhanced_recommendations_view(recommendations, lang)
            
            if not message_text or not reply_markup:
                print(f"❌ UI creation failed for {lang}")
                return False
                
            print(f"   ✅ Message: {len(message_text)} chars")
            print(f"   ✅ Buttons: {len(reply_markup.inline_keyboard)} rows")
            
            # Check for language-specific content
            if lang == 'kh':
                if 'អនុសាសន៍មុខវិជ្ជាសិក្សា' not in message_text:
                    print("❌ Missing Khmer content")
                    return False
            else:
                if 'Program Recommendations' not in message_text:
                    print("❌ Missing English content")
                    return False
                    
        print("✅ UI works in both languages")
        return True
        
    except Exception as e:
        print(f"❌ UI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all end-to-end tests"""
    print("🚀 Starting EduGuideBot end-to-end recommendations test\n")
    
    # Test bot handler
    e2e_test = await test_bot_recommendations_e2e()
    
    # Test UI with real data
    ui_test = await test_ui_with_real_data()
    
    print(f"\n📊 Test Results:")
    print(f"   Bot Handler E2E: {'✅ PASS' if e2e_test else '❌ FAIL'}")
    print(f"   UI with Real Data: {'✅ PASS' if ui_test else '❌ FAIL'}")
    
    if e2e_test and ui_test:
        print("\n🎉 All end-to-end tests passed!")
        print("✅ The bot recommendations system is working correctly!")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
