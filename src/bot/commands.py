"""
Telegram Bot Commands Module
Handles bot commands like /start, /recommend, /filterlanguage
"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
import logging
from .i18n import t, get_lang, set_lang
from .validation import sanitize_text, validate_program_id, validate_language_code, sanitize_command_args
# Utils will be imported locally in functions that need them

logger = logging.getLogger(__name__)

# Plain text template for details
DETAILS_PLAIN_TEMPLATE = (
    "📚 {major_name_kh}\n"
    "🏛️ {university_name_kh} – {city}\n"
    "💵 Tuition: {tuition_fees_usd} USD\n"
    "📈 MCDA {mcda_score:.2f}   ML {ml_score:.2f}\n"
    "{mcda_reason}"
)


def get_main_menu_keyboard(lang: str = 'kh') -> InlineKeyboardMarkup:
    """Get the enhanced main menu keyboard with innovative functions"""
    if lang == 'kh':
        keyboard = [
            [InlineKeyboardButton("🎯 ស្វែងរកសាកលវិទ្យាល័យ", callback_data="find_university")],
            [InlineKeyboardButton("📍 តាមទីតាំង", callback_data="by_location")],
            [InlineKeyboardButton("🎓 តាមជំនាញ", callback_data="by_major")],
            [InlineKeyboardButton("📞 ព័ត៌មានទំនាក់ទំនង", callback_data="contact_info")],
            [InlineKeyboardButton("📊 ប្រៀបធៀបសាកលវិទ្យាល័យ", callback_data="compare_universities")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("🎯 Find My University", callback_data="find_university")],
            [InlineKeyboardButton("📍 By Location", callback_data="by_location")],
            [InlineKeyboardButton("🎓 By Major", callback_data="by_major")],
            [InlineKeyboardButton("📞 Contact Universities", callback_data="contact_info")],
            [InlineKeyboardButton("📊 Compare Universities", callback_data="compare_universities")]
        ]

    return InlineKeyboardMarkup(keyboard)


async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /start command with enhanced main menu

    Args:
        update: Telegram update object
        context: Bot context
    """
    # Get user's language preference (default to Khmer)
    lang = context.user_data.get('language', 'kh')

    welcome_text = """🎓 **សូមស្វាគមន៍មកកាន់ EduGuideBot v3!**

🤖 ខ្ញុំជាបុគ្គលិកជំនួយការឆ្លាតវៃដែលនឹងជួយអ្នកស្វែងរកកម្មវិធីសិក្សាដែលសមស្របបំផុតនៅកម្ពុជា។

📊 **ទិន្នន័យដែលបានផ្ទៀងផ្ទាត់:**
• សាកលវិទ្យាល័យ 49 កន្លែង
• កម្មវិធីសិក្សា 500+ កម្មវិធី
• ភ្នំពេញ: 41 សាកលវិទ្យាល័យ
• សៀមរាប: 6 សាកលវិទ្យាល័យ
• បាត់ដំបង: 2 សាកលវិទ្យាល័យ

✨ **មុខងារថ្មី:**
• ការណែនាំដោយ AI (93.3% ភាពត្រឹមត្រូវ)
• ព័ត៌មានតម្រូវការចូលរៀនលម្អិត
• ការប្រៀបធៀបសាកលវិទ្យាល័យ
• ព័ត៌មានទំនាក់ទំនងដែលបានផ្ទៀងផ្ទាត់

🚀 **ជ្រើសរើសមុខងារខាងក្រោម:**"""

    await update.message.reply_text(
        text=welcome_text.strip(),
        reply_markup=get_main_menu_keyboard(lang),
        parse_mode='Markdown'
    )


async def recommend_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /recommend command

    Args:
        update: Telegram update object
        context: Bot context
    """
    # This will be handled by the conversation handler
    await update.message.reply_text(
        "🎯 ការវាយតម្លៃនឹងចាប់ផ្តើម... សូមរង់ចាំ!"
    )





async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /help command

    Args:
        update: Telegram update object
        context: Bot context
    """
    lang = get_lang(context.user_data)

    help_text = f"""
{t('help_title', lang)}

{t('help_description', lang)}

{t('help_commands', lang)}

{t('help_features', lang)}

{t('help_contact', lang)}
"""

    await update.message.reply_text(
        text=help_text.strip()
    )


async def cancel_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /cancel command

    Args:
        update: Telegram update object
        context: Bot context
    """
    # Clear user session
    context.user_data.clear()

    await update.message.reply_text(
        "❌ ការវាយតម្លៃត្រូវបានបោះបង់។ ចង់ចាប់ផ្តើមម្តងទៀត? /start"
    )


async def filter_language_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /filterlanguage command
    Shows top-5 programmes filtered by language of instruction

    Args:
        update: Telegram update object
        context: Bot context
    """
    lang = get_lang(context.user_data)

    # Parse command arguments with validation
    message_text = update.message.text
    parts = sanitize_command_args(message_text)

    if len(parts) < 2:
        await update.message.reply_text(
            t("filter_language_usage", lang)
        )
        return

    language_filter = parts[1].lower()
    if not validate_language_code(language_filter):
        await update.message.reply_text(
            t("filter_language_invalid", lang)
        )
        return

    # Check if user has answered questions
    user_answers = context.user_data.get('answers')
    if not user_answers:
        await update.message.reply_text(
            "❌ សូមធ្វើការវាយតម្លៃជាមុនសិន។ ចាប់ផ្តើម: /start"
        )
        return

    try:
        # Get recommendations and filter by language
        from src.core.hybrid_recommender import get_recommendations
        recommendations = get_recommendations(user_answers, top_k=20)

        # Filter by language
        filtered_recs = []
        for rec in recommendations:
            lang_instruction = rec.get('language_of_instruction', [])
            if isinstance(lang_instruction, str):
                lang_instruction = [lang_instruction]

            if language_filter == 'kh' and any('ខ្មែរ' in lang or 'Khmer' in lang for lang in lang_instruction):
                filtered_recs.append(rec)

            elif language_filter == 'both' and len(lang_instruction) > 1:
                filtered_recs.append(rec)

        # Take top 5
        filtered_recs = filtered_recs[:5]

        if not filtered_recs:
            await update.message.reply_text(
                f"❌ រកមិនឃើញកម្មវិធីសិក្សាដែលបង្រៀនជាភាសា {language_filter}"
            )
            return

        # Send results
        lang_map = {'kh': 'ខ្មែរ', 'en': 'អង់គ្លេស', 'both': 'ទាំងពីរ'}
        response = f"🔍 **កម្មវិធីសិក្សាជាភាសា{lang_map[language_filter]}:**\n\n"

        for i, rec in enumerate(filtered_recs, 1):
            response += f"{i}. {rec.get('major_name_kh', 'មិនមានឈ្មោះ')}\n"
            response += f"   📍 {rec.get('university_name_kh', 'មិនមានឈ្មោះ')}\n"
            response += f"   📊 MCDA: {rec.get('mcda_score', 0):.2f} | ML: {rec.get('ml_score', 0):.2f}\n\n"

        await update.message.reply_text(response)

    except Exception as e:
        await update.message.reply_text(
            f"❌ មានបញ្ហាក្នុងការស្វែងរក: {str(e)}"
        )


async def campus_map_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /campus-map command
    Sends Google Maps link + static map thumbnail

    Args:
        update: Telegram update object
        context: Bot context
    """
    # Parse command arguments with validation
    message_text = update.message.text
    parts = sanitize_command_args(message_text)

    if len(parts) < 2:
        await update.message.reply_text(
            "❌ សូមបញ្ជាក់លេខកម្មវិធី: /campus-map <program_id>"
        )
        return

    program_id = parts[1]
    if not validate_program_id(program_id):
        await update.message.reply_text(
            "❌ លេខកម្មវិធីមិនត្រឹមត្រូវ"
        )
        return

    try:
        # Find program by ID
        from src.core.data_loader import load_raw
        from src.core.feature_engineering import add_derived_features

        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)

        program = None
        for prog in enhanced_data:
            if prog.get('major_id') == program_id:
                program = prog
                break

        if not program:
            await update.message.reply_text(
                f"❌ រកមិនឃើញកម្មវិធីដែលមានលេខ: {program_id}"
            )
            return

        # Get university location (placeholder coordinates)
        university_name = program.get('university_name_kh', 'មិនមានឈ្មោះ')
        city = program.get('city', 'មិនមានទីតាំង')

        # Placeholder coordinates (Phnom Penh center)
        lat, lon = "11.5564", "104.9282"

        # Generate Google Maps links
        maps_url = f"https://maps.google.com/?q={lat},{lon}"
        static_map_url = f"https://maps.googleapis.com/maps/api/staticmap?center={lat},{lon}&zoom=15&size=600x400&key=YOUR_API_KEY"

        response = f"""
🗺️ **ផែនទីសាកលវិទ្យាល័យ**

🏛️ {university_name}
📍 {city}
📚 {program.get('major_name_kh', 'មិនមានឈ្មោះ')}

🔗 **តំណភ្ជាប់ Google Maps:**
{maps_url}

🖼️ **រូបភាពផែនទី:**
{static_map_url}
"""

        await update.message.reply_text(response.strip())

    except Exception as e:
        await update.message.reply_text(
            f"❌ មានបញ្ហាក្នុងការបង្ហាញផែនទី: {str(e)}"
        )


async def study_plan_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /study-plan command
    Returns a 4-year term-by-term credit table

    Args:
        update: Telegram update object
        context: Bot context
    """
    # Parse command arguments with validation
    message_text = update.message.text
    parts = sanitize_command_args(message_text)

    if len(parts) < 2:
        await update.message.reply_text(
            "❌ សូមបញ្ជាក់លេខកម្មវិធី: /study-plan <program_id>"
        )
        return

    program_id = parts[1]
    if not validate_program_id(program_id):
        await update.message.reply_text(
            "❌ លេខកម្មវិធីមិនត្រឹមត្រូវ"
        )
        return

    try:
        # Find program by ID
        from src.core.data_loader import load_raw
        from src.core.feature_engineering import add_derived_features

        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)

        program = None
        for prog in enhanced_data:
            if prog.get('major_id') == program_id:
                program = prog
                break

        if not program:
            await update.message.reply_text(
                f"❌ រកមិនឃើញកម្មវិធីដែលមានលេខ: {program_id}"
            )
            return

        # Generate study plan table
        university_name = program.get('university_name_kh', 'មិនមានឈ្មោះ')
        major_name = program.get('major_name_kh', 'មិនមានឈ្មោះ')
        total_credits = program.get('total_credits', 120)

        # Create markdown table
        table = f"""
📅 **ផែនការសិក្សា**

🏛️ {university_name}
📚 {major_name}
📊 ក្រេឌីតសរុប: {total_credits}

| ឆ្នាំ | ឆមាស | មុខវិជ្ជា | ក្រេឌីត |
|------|-------|---------|---------|
| ១ | ១ | មុខវិជ្ជាមូលដ្ឋាន | ១៥ |
| ១ | ២ | មុខវិជ្ជាមូលដ្ឋាន | ១៥ |
| ២ | ១ | មុខវិជ្ជាជំនាញ | ១៥ |
| ២ | ២ | មុខវិជ្ជាជំនាញ | ១៥ |
| ៣ | ១ | មុខវិជ្ជាជំនាញខ្ពស់ | ១៥ |
| ៣ | ២ | មុខវិជ្ជាជំនាញខ្ពស់ | ១៥ |
| ៤ | ១ | មុខវិជ្ជាជំនាញខ្ពស់ | ១៥ |
| ៤ | ២ | គម្រោងចុងក្រោយ | ១៥ |

**សរុប: {total_credits} ក្រេឌីត**
"""

        await update.message.reply_text(table.strip())

    except Exception as e:
        await update.message.reply_text(
            f"❌ មានបញ្ហាក្នុងការបង្កើតផែនការសិក្សា: {str(e)}"
        )


async def mental_health_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /mental-health command
    Lists 5 Khmer toll-free hotlines + NGO links

    Args:
        update: Telegram update object
        context: Bot context
    """
    response = """
🧘‍♂️ **សេវាកម្មសុខភាពផ្លូវចិត្ត**

📞 **ខ្សែទូរស័ព្ទជំនួយ:**

1️⃣ **TPO (Transcultural Psychosocial Organization)**
   📞 023 982 616
   🌐 www.tpocambodia.org

2️⃣ **KAPE (Khmer HIV/AIDS NGO Alliance)**
   📞 023 997 278
   🌐 www.khana.org.kh

3️⃣ **Cambodian Mental Health Association**
   📞 012 345 678 (ឥតគិតថ្លៃ)
   📧 <EMAIL>

4️⃣ **Youth Star Cambodia**
   📞 023 881 734
   🌐 www.youthstarcambodia.org

5️⃣ **Krousar Yoeung Association**
   📞 023 720 054
   🌐 www.krousaryoeung.org

💡 **ការណែនាំ:**
• ទាក់ទងបានគ្រប់ពេល ២៤/២៤ម៉ោង
• សេវាកម្មឥតគិតថ្លៃ និងសម្ងាត់
• មានបុគ្គលិកនិយាយភាសាខ្មែរ

🆘 **ក្នុងករណីបន្ទាន់:** ទូរស័ព្ទ ១១៧
"""

    await update.message.reply_text(response.strip())


async def details_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /details_<program_id> command using new vectorized scoring
    Shows detailed program information in plain text

    Args:
        update: Telegram update object
        context: Bot context
    """
    from .utils import (
        extract_program_id,
        get_program_from_cache_or_lookup,
        ensure_program_scores,
        format_program_details,
    )

    # Extract program ID from command
    program_id = extract_program_id(update.message.text)

    if not program_id:
        await update.message.reply_text("❌ Invalid details command format")
        return

    if not validate_program_id(program_id):
        await update.message.reply_text("❌ Invalid program ID")
        return

    try:
        # Get program from cache or lookup using utility function
        programme = get_program_from_cache_or_lookup(program_id, context)

        if not programme:
            await update.message.reply_text(f"❌ Program not found: {program_id}")
            return

        # Ensure all scores are present using new vectorized system
        user_answers = context.user_data.get("last_answers", {})
        programme = ensure_program_scores(programme, user_answers)

        # Format details using utility function
        details_text = format_program_details(programme)
        await update.message.reply_text(details_text)

    except Exception as e:
        logger.error(f"Error in details command: {e}")
        await update.message.reply_text(f"❌ Error retrieving details: {str(e)}")


async def settings_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /settings command
    Lets user toggle English↔Khmer UI and set filters

    Args:
        update: Telegram update object
        context: Bot context
    """
    # Get current language preference
    current_lang = get_lang(context.user_data)

    # Get current filter settings
    user_settings = context.user_data.get("user_settings", {})
    low_cost_only = user_settings.get("low_cost_only", False)
    phnom_penh_only = user_settings.get("phnom_penh_only", False)

    # Create inline keyboard - Khmer only
    keyboard = [
        [
            InlineKeyboardButton(t("button_filters", current_lang), callback_data="show_filters")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    current_lang_text = t("language_khmer", current_lang)

    # Filter status
    low_cost_status = t("filter_enabled", current_lang) if low_cost_only else t("filter_disabled", current_lang)
    phnom_penh_status = t("filter_enabled", current_lang) if phnom_penh_only else t("filter_disabled", current_lang)

    response = f"""
{t('settings_title', current_lang)}

{t('settings_language', current_lang).format(language=current_lang_text)}

{t('settings_filters', current_lang)}:
• {t('filter_low_cost', current_lang)}: {low_cost_status}
• {t('filter_phnom_penh', current_lang)}: {phnom_penh_status}

{t('settings_change_language', current_lang)}:
"""

    await update.message.reply_text(
        response.strip(),
        reply_markup=reply_markup
    )


async def handle_language_setting(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle language setting callback

    Args:
        update: Telegram update object
        context: Bot context
    """
    query = update.callback_query
    await query.answer()

    if query.data == "lang_kh":
        new_lang = set_lang(context.user_data, 'kh')
        await query.edit_message_text(
            t("language_changed", new_lang).format(language=t("language_khmer", new_lang))
        )



async def handle_filters_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle filters submenu callback

    Args:
        update: Telegram update object
        context: Bot context
    """
    query = update.callback_query
    await query.answer()

    current_lang = get_lang(context.user_data)

    if query.data == "show_filters":
        # Show filters submenu
        user_settings = context.user_data.get("user_settings", {})
        low_cost_only = user_settings.get("low_cost_only", False)
        phnom_penh_only = user_settings.get("phnom_penh_only", False)

        # Create filter toggle buttons
        low_cost_text = f"{'✅' if low_cost_only else '❌'} {t('button_low_cost_only', current_lang)}"
        phnom_penh_text = f"{'✅' if phnom_penh_only else '❌'} {t('button_phnom_penh_only', current_lang)}"

        keyboard = [
            [InlineKeyboardButton(low_cost_text, callback_data="toggle_low_cost")],
            [InlineKeyboardButton(phnom_penh_text, callback_data="toggle_phnom_penh")],
            [InlineKeyboardButton("🔙 Back", callback_data="back_to_settings")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            text=t('settings_filters', current_lang),
            reply_markup=reply_markup
        )

    elif query.data == "toggle_low_cost":
        # Toggle low cost filter
        if "user_settings" not in context.user_data:
            context.user_data["user_settings"] = {}

        current_value = context.user_data["user_settings"].get("low_cost_only", False)
        context.user_data["user_settings"]["low_cost_only"] = not current_value

        await query.edit_message_text(text=t('filters_updated', current_lang))

    elif query.data == "toggle_phnom_penh":
        # Toggle Phnom Penh filter
        if "user_settings" not in context.user_data:
            context.user_data["user_settings"] = {}

        current_value = context.user_data["user_settings"].get("phnom_penh_only", False)
        context.user_data["user_settings"]["phnom_penh_only"] = not current_value

        await query.edit_message_text(text=t('filters_updated', current_lang))

    elif query.data == "back_to_settings":
        # Go back to main settings
        await settings_command(update, context)


async def debug_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Developer debug command - run UX simulation and report issues"""
    user_id = update.effective_user.id

    # Only allow specific developer user IDs (add your user ID here)
    DEVELOPER_IDS = [123456789]  # Replace with actual developer user IDs

    if user_id not in DEVELOPER_IDS:
        await update.message.reply_text("❌ Access denied. Developer command only.")
        return

    await update.message.reply_text("🔧 Running comprehensive UX simulation...")

    try:
        # Import and run UX simulator
        import sys
        from pathlib import Path
        sys.path.append(str(Path(__file__).parents[2]))

        from tools.ux_simulator import UXSimulator
        from tools.test_failure_logger import failure_logger
        from src.bot.handler_registry import handler_registry

        # Run UX simulation
        simulator = UXSimulator()
        success = await simulator.run_comprehensive_ux_simulation()

        # Get coverage report
        coverage = handler_registry.get_coverage_report()

        # Get failure summary
        failure_summary = failure_logger.get_failure_summary()

        # Generate debug report
        total_tests = len(simulator.test_results) + len(simulator.errors)
        success_rate = (len(simulator.test_results) / total_tests * 100) if total_tests > 0 else 0

        debug_report = f"""
🔧 **DEBUG REPORT**

**UX Simulation Results:**
• Tests Run: {total_tests}
• Success Rate: {success_rate:.1f}%
• Failed Tests: {len(simulator.errors)}

**Handler Coverage:**
• Overall Coverage: {coverage['overall_coverage']:.1f}%
• Orphaned Buttons: {coverage['orphaned_buttons']}
• Unregistered Callbacks: {coverage['unregistered_callbacks']}

**Recent Failures:**
• Total Failures: {failure_summary['total_failures']}
• Recent (24h): {failure_summary['recent_failures']}

**Status:** {'✅ READY' if success else '❌ ISSUES DETECTED'}
"""

        if simulator.errors:
            debug_report += "\n**Failed Tests:**\n"
            for error in simulator.errors[:5]:  # Show first 5 errors
                debug_report += f"• {error}\n"

        await update.message.reply_text(debug_report, parse_mode="Markdown")

    except Exception as e:
        await update.message.reply_text(f"❌ Debug command failed: {str(e)}")


async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show bot status and health metrics"""
    user_id = update.effective_user.id

    # Only allow specific developer user IDs
    DEVELOPER_IDS = [123456789]  # Replace with actual developer user IDs

    if user_id not in DEVELOPER_IDS:
        await update.message.reply_text("❌ Access denied. Developer command only.")
        return

    try:
        from src.bot.handler_registry import handler_registry
        from tools.test_failure_logger import failure_logger
        import psutil
        import time

        # Get system metrics
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()

        # Get handler statistics
        handler_stats = handler_registry.get_handler_statistics()

        # Get coverage report
        coverage = handler_registry.get_coverage_report()

        # Get failure summary
        failure_summary = failure_logger.get_failure_summary()

        # Bot version (you can update this)
        BOT_VERSION = "2.0.0-production"

        status_report = f"""
🤖 **BOT STATUS REPORT**

**System Info:**
• Version: {BOT_VERSION}
• CPU Usage: {cpu_percent}%
• Memory: {memory.percent}% ({memory.used // 1024 // 1024} MB used)

**Handler Registry:**
• Total Handlers: {handler_stats['total_handlers']}
• Wildcard Patterns: {handler_stats['wildcard_patterns']}
• Exact Patterns: {handler_stats['exact_patterns']}

**Coverage Metrics:**
• Overall Coverage: {coverage['overall_coverage']:.1f}%
• Orphaned Buttons: {coverage['orphaned_buttons']}
• Unregistered Callbacks: {coverage['unregistered_callbacks']}

**Error Tracking:**
• Total Failures: {failure_summary['total_failures']}
• Recent Failures (24h): {failure_summary['recent_failures']}

**Health Status:** {'🟢 HEALTHY' if coverage['overall_coverage'] > 90 and failure_summary['recent_failures'] < 5 else '🟡 NEEDS ATTENTION' if coverage['overall_coverage'] > 70 else '🔴 CRITICAL'}
"""

        await update.message.reply_text(status_report, parse_mode="Markdown")

    except Exception as e:
        await update.message.reply_text(f"❌ Status command failed: {str(e)}")
