"""
University Features Handlers
Enhanced university exploration features for EduGuideBot v3.
"""

import logging
from typing import Dict, List, Any, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Add project root to path
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.core.data_loader import load_raw
from src.core.feature_engineering import add_derived_features
from src.bot.telegram_safe import safe_answer_callback, safe_edit_message
from src.bot.i18n import t, get_lang

logger = logging.getLogger(__name__)

def get_all_universities() -> Dict[str, Dict]:
    """Get all unique universities from our data."""
    try:
        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)
        
        universities = {}
        for program in enhanced_data:
            uni_id = program.get('university_id')
            if uni_id and uni_id not in universities:
                universities[uni_id] = {
                    'id': uni_id,
                    'name_kh': program.get('university_name_kh', 'មិនមានឈ្មោះ'),
                    'name_en': program.get('university_name_en', 'Unknown'),
                    'city': program.get('city', 'មិនមានទីតាំង'),
                    'location': program.get('location', 'មិនមានទីតាំង'),
                    'type': program.get('university_type', 'មិនមានទិន្នន័យ'),
                    'programs_count': 0
                }
            
            if uni_id in universities:
                universities[uni_id]['programs_count'] += 1
        
        return universities
        
    except Exception as e:
        logger.error(f"Error loading universities: {e}")
        return {}

def get_universities_by_city() -> Dict[str, List[Dict]]:
    """Group universities by city using source file path."""
    try:
        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)

        by_city = {'Phnom Penh': [], 'Siem Reap': [], 'Battambang': []}
        universities = {}

        for program in enhanced_data:
            uni_id = program.get('university_id')
            source_file = program.get('source_file', '')

            if uni_id and uni_id not in universities:
                # Determine city from program data first, then fallback to source file path
                program_city = program.get('city', '').strip()

                # Map Khmer city names to English
                city_mapping = {
                    'ភ្នំពេញ': 'Phnom Penh',
                    'សៀមរាប': 'Siem Reap',
                    'បាត់ដំបង': 'Battambang',
                    'Phnom Penh': 'Phnom Penh',
                    'Siem Reap': 'Siem Reap',
                    'Battambang': 'Battambang'
                }

                city = city_mapping.get(program_city, None)

                # Fallback to source file path if city not found in data
                if not city:
                    city = 'Phnom Penh'  # default
                    if '/PP/' in source_file:
                        city = 'Phnom Penh'
                    elif '/SR/' in source_file:
                        city = 'Siem Reap'
                    elif '/BTB/' in source_file:
                        city = 'Battambang'

                universities[uni_id] = {
                    'id': uni_id,
                    'name_kh': program.get('university_name_kh', 'មិនមានឈ្មោះ'),
                    'name_en': program.get('university_name_en', 'Unknown'),
                    'city': city,
                    'location': program.get('address', program.get('city', city)),
                    'type': program.get('university_type', 'មិនមានទិន្នន័យ'),
                    'programs_count': 0,
                    'phone': program.get('phone', 'មិនមានទិន្នន័យ'),
                    'email': program.get('email', 'មិនមានទិន្នន័យ'),
                    'website': program.get('website', 'មិនមានទិន្នន័យ')
                }

                by_city[city].append(universities[uni_id])

            if uni_id in universities:
                universities[uni_id]['programs_count'] += 1

        return by_city

    except Exception as e:
        logger.error(f"Error grouping universities by city: {e}")
        return {'Phnom Penh': [], 'Siem Reap': [], 'Battambang': []}

async def handle_find_university(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle find university - starts assessment."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    
    text = """🎯 **ការស្វែងរកសាកលវិទ្យាល័យដោយ AI**

🤖 ខ្ញុំនឹងសួរអ្នកនូវសំណួរ 16 ដើម្បីស្វែងរកសាកលវិទ្យាល័យដែលសមស្របបំផុតសម្រាប់អ្នក។

✨ **ការវាយតម្លៃនេះរួមមាន:**
• ចំណាប់អារម្មណ៍ និងជំនាញ
• ថវិកា និងទីតាំង
• គោលដៅអាជីព
• របៀបរៀនដែលចូលចិត្ត

⏱️ **ពេលវេលា:** ប្រហែល 5-10 នាទី
🎯 **ភាពត្រឹមត្រូវ:** 93.3%

🚀 **ចាប់ផ្តើមការវាយតម្លៃ?**"""

    keyboard = [
        [InlineKeyboardButton("✅ ចាប់ផ្តើម", callback_data="start_assessment")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]
    
    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_by_location(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle search by location."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    by_city = get_universities_by_city()
    
    text = f"""📍 **ស្វែងរកតាមទីតាំង**

🏛️ **សាកលវិទ្យាល័យនៅកម្ពុជា:**
• ភ្នំពេញ: {len(by_city['Phnom Penh'])} សាកលវិទ្យាល័យ
• សៀមរាប: {len(by_city['Siem Reap'])} សាកលវិទ្យាល័យ
• បាត់ដំបង: {len(by_city['Battambang'])} សាកលវិទ្យាល័យ

📊 **សរុប:** {sum(len(unis) for unis in by_city.values())} សាកលវិទ្យាល័យ

🎯 **ជ្រើសរើសទីតាំងដែលអ្នកចង់សិក្សា:**"""

    keyboard = [
        [InlineKeyboardButton(f"📍 ភ្នំពេញ ({len(by_city['Phnom Penh'])} សាកលវិទ្យាល័យ)", callback_data="location_PP")],
        [InlineKeyboardButton(f"📍 សៀមរាប ({len(by_city['Siem Reap'])} សាកលវិទ្យាល័យ)", callback_data="location_SR")],
        [InlineKeyboardButton(f"📍 បាត់ដំបង ({len(by_city['Battambang'])} សាកលវិទ្យាល័យ)", callback_data="location_BTB")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]
    
    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_location_selection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle specific location selection."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    location_code = query.data.split('_')[1]
    city_map = {'PP': 'Phnom Penh', 'SR': 'Siem Reap', 'BTB': 'Battambang'}
    city_kh_map = {'PP': 'ភ្នំពេញ', 'SR': 'សៀមរាប', 'BTB': 'បាត់ដំបង'}
    
    city = city_map.get(location_code, 'Unknown')
    city_kh = city_kh_map.get(location_code, 'មិនស្គាល់')
    
    by_city = get_universities_by_city()
    universities = by_city.get(city, [])
    
    if not universities:
        text = f"❌ រកមិនឃើញសាកលវិទ្យាល័យនៅ{city_kh}"
        keyboard = [[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="by_location")]]
    else:
        text = f"🏛️ **សាកលវិទ្យាល័យនៅ{city_kh}** ({len(universities)} កន្លែង)\n\n"
        text += "🎯 **ជ្រើសរើសសាកលវិទ្យាល័យដើម្បីមើលព័ត៌មានលម្អិត:**"

        keyboard = []
        for uni in universities[:15]:  # Show more universities as buttons
            # Create button with university name and program count
            button_text = f"🏛️ {uni['name_kh']} ({uni['programs_count']} កម្មវិធី)"
            keyboard.append([InlineKeyboardButton(button_text, callback_data=f"uni_details_{uni['id']}")])

        keyboard.append([InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="by_location")])
    
    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_by_major(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle search by major field."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    text = """🎓 **ស្វែងរកតាមជំនាញ**

📚 **ជ្រើសរើសផ្នែកសិក្សាដែលអ្នកចាប់អារម្មណ៍:**

🔬 **STEM** - វិទ្យាសាស្ត្រ, បច្ចេកវិទ្យា, វិស្វកម្ម, គណិតវិទ្យា
💼 **ពាណិជ្ជកម្ម** - គ្រប់គ្រង, សេដ្ឋកិច្ច, ហិរញ្ញវត្ថុ, ទីផ្សារ
🏥 **សុខភាព** - វេជ្ជសាស្ត្រ, ឱសថ, ពេទ្យបំរុង, សុខភាពសាធារណៈ
🎨 **សិល្បៈ** - ការរចនា, ប្រព័ន្ធផ្សព្វផ្សាយ, ភាសា, វប្បធម៌
🏛️ **វិទ្យាសាស្ត្រសង្គម** - ចិត្តវិទ្យា, នយោបាយ, សង្គមវិទ្យា
📚 **អប់រំ** - បង្រៀន, គ្រប់គ្រងអប់រំ, កម្មវិធីសិក្សា"""

    keyboard = [
        [InlineKeyboardButton("🔬 STEM (វិទ្យាសាស្ត្រ & បច្ចេកវិទ្យា)", callback_data="major_stem")],
        [InlineKeyboardButton("💼 ពាណិជ្ជកម្ម & សេដ្ឋកិច្ច", callback_data="major_business")],
        [InlineKeyboardButton("🏥 សុខភាព & វេជ្ជសាស្ត្រ", callback_data="major_health")],
        [InlineKeyboardButton("🎨 សិល្បៈ & មនុស្សសាស្ត្រ", callback_data="major_arts")],
        [InlineKeyboardButton("🏛️ វិទ្យាសាស្ត្រសង្គម", callback_data="major_social")],
        [InlineKeyboardButton("📚 អប់រំ", callback_data="major_education")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]
    
    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_major_selection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle specific major field selection."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    major_field = query.data.split('_')[1]
    
    # Map major fields to search terms
    field_map = {
        'stem': ['engineering', 'computer', 'science', 'technology', 'mathematics'],
        'business': ['business', 'management', 'economics', 'finance', 'accounting'],
        'health': ['medicine', 'nursing', 'pharmacy', 'health', 'medical'],
        'arts': ['art', 'design', 'media', 'language', 'literature'],
        'social': ['psychology', 'sociology', 'political', 'social'],
        'education': ['education', 'teaching', 'pedagogy']
    }
    
    field_kh_map = {
        'stem': 'STEM (វិទ្យាសាស្ត្រ & បច្ចេកវិទ្យា)',
        'business': 'ពាណិជ្ជកម្ម & សេដ្ឋកិច្ច',
        'health': 'សុខភាព & វេជ្ជសាស្ត្រ',
        'arts': 'សិល្បៈ & មនុស្សសាស្ត្រ',
        'social': 'វិទ្យាសាស្ត្រសង្គម',
        'education': 'អប់រំ'
    }
    
    try:
        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)
        
        # Filter programs by field
        search_terms = field_map.get(major_field, [])
        matching_programs = []
        
        for program in enhanced_data:
            major_name = program.get('major_name_en', '').lower()
            if any(term in major_name for term in search_terms):
                matching_programs.append(program)
        
        # Get unique universities offering these programs
        universities = {}
        for program in matching_programs:
            uni_id = program.get('university_id')
            if uni_id and uni_id not in universities:
                universities[uni_id] = {
                    'id': uni_id,
                    'name_kh': program.get('university_name_kh', 'មិនមានឈ្មោះ'),
                    'city': program.get('city', 'មិនមានទីតាំង'),
                    'programs': []
                }
            
            if uni_id in universities:
                universities[uni_id]['programs'].append(program.get('major_name_kh', 'មិនមានឈ្មោះ'))
        
        field_name = field_kh_map.get(major_field, 'មិនស្គាល់')
        
        if not universities:
            text = f"❌ រកមិនឃើញសាកលវិទ្យាល័យសម្រាប់ {field_name}"
            keyboard = [[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="by_major")]]
        else:
            text = f"🎓 **សាកលវិទ្យាល័យផ្តល់ {field_name}** ({len(universities)} កន្លែង)\n\n"
            text += "🎯 **ជ្រើសរើសសាកលវិទ្យាល័យដើម្បីមើលព័ត៌មានលម្អិត:**"

            keyboard = []
            for uni_id, uni_data in list(universities.items())[:12]:  # Show more as buttons
                # Create button with university name and program count
                button_text = f"🏛️ {uni_data['name_kh']} ({len(uni_data['programs'])} កម្មវិធី)"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"uni_details_{uni_id}")])

            keyboard.append([InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="by_major")])
        
        await safe_edit_message(
            query,
            text.strip(),
            InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )
        
    except Exception as e:
        logger.error(f"Error in major selection: {e}")
        await safe_edit_message(
            query,
            "❌ មានបញ្ហាក្នុងការស្វែងរក។ សូមព្យាយាមម្តងទៀត។",
            InlineKeyboardMarkup([[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="by_major")]])
        )

async def handle_contact_info(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle contact information request."""
    query = update.callback_query
    await safe_answer_callback(query)

    text = """📞 **ព័ត៌មានទំនាក់ទំនងសាកលវិទ្យាល័យ**

🔍 **របៀបទទួលបានព័ត៌មានទំនាក់ទំនង:**

1️⃣ **ស្វែងរកសាកលវិទ្យាល័យជាក់លាក់**
   • ប្រើមុខងារ "តាមទីតាំង" ឬ "តាមជំនាញ"
   • ជ្រើសរើសសាកលវិទ្យាល័យ
   • ចុចលើ "ព័ត៌មានទំនាក់ទំនង"

2️⃣ **ព័ត៌មានដែលមាន:**
   📞 លេខទូរស័ព្ទ
   📧 អ៊ីមែល
   🌐 គេហទំព័រ
   📍 អាសយដ្ឋាន
   📘 Facebook Page

3️⃣ **សាកលវិទ្យាល័យដែលបានផ្ទៀងផ្ទាត់:**
   ✅ ព័ត៌មានទំនាក់ទំនងត្រឹមត្រូវ 100%
   ✅ បានស្រាវជ្រាវពីគេហទំព័រផ្លូវការ
   ✅ ធ្វើបច្ចុប្បន្នភាពទៀងទាត់"""

    keyboard = [
        [InlineKeyboardButton("📍 ស្វែងរកតាមទីតាំង", callback_data="by_location")],
        [InlineKeyboardButton("🎓 ស្វែងរកតាមជំនាញ", callback_data="by_major")],
        [InlineKeyboardButton("🏛️ មើលសាកលវិទ្យាល័យទាំងអស់", callback_data="explore_all")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]

    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_compare_universities(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle university comparison feature."""
    query = update.callback_query
    await safe_answer_callback(query)

    text = """📊 **ប្រៀបធៀបសាកលវិទ្យាល័យ**

🔍 **មុខងារប្រៀបធៀប:**

📋 **ការប្រៀបធៀបរួមមាន:**
• 💰 តម្លៃសិក្សា (USD)
• 📍 ទីតាំង និងភូមិសាស្ត្រ
• 🎓 កម្មវិធីសិក្សាដែលមាន
• 📞 ព័ត៌មានទំនាក់ទំនង
• 🏛️ ប្រភេទសាកលវិទ្យាល័យ (សាធារណៈ/ឯកជន)
• 📅 ឆ្នាំបង្កើត
• 📋 តម្រូវការចូលរៀន

⚡ **របៀបប្រើប្រាស់:**
1. ជ្រើសរើសសាកលវិទ្យាល័យទី 1
2. ជ្រើសរើសសាកលវិទ្យាល័យទី 2
3. មើលការប្រៀបធៀបលម្អិត

🎯 **ចាប់ផ្តើមប្រៀបធៀប:**"""

    keyboard = [
        [InlineKeyboardButton("🏛️ ជ្រើសរើសសាកលវិទ្យាល័យ", callback_data="select_for_comparison")],
        [InlineKeyboardButton("📍 ប្រៀបធៀបតាមទីតាំង", callback_data="compare_by_location")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]

    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_explore_all(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle explore all universities feature."""
    query = update.callback_query
    await safe_answer_callback(query)

    by_city = get_universities_by_city()
    total_unis = sum(len(unis) for unis in by_city.values())

    text = f"""🏛️ **បង្ហាញសាកលវិទ្យាល័យទាំងអស់**

📊 **ទិន្នន័យសាកលវិទ្យាល័យនៅកម្ពុជា:**

🏙️ **ភ្នំពេញ:** {len(by_city['Phnom Penh'])} សាកលវិទ្យាល័យ
   • សាកលវិទ្យាល័យសាធារណៈ និងឯកជន
   • កម្មវិធីសិក្សាចម្រុះ
   • មជ្ឈមណ្ឌលអប់រំសំខាន់

🏛️ **សៀមរាប:** {len(by_city['Siem Reap'])} សាកលវិទ្យាល័យ
   • ផ្តោតលើទេសចរណ៍ និងវប្បធម៌
   • កម្មវិធីអន្តរជាតិ

🌾 **បាត់ដំបង:** {len(by_city['Battambang'])} សាកលវិទ្យាល័យ
   • ផ្តោតលើកសិកម្ម និងអភិវឌ្ឍន៍ជនបទ

📈 **សរុប:** {total_unis} សាកលវិទ្យាល័យ
🎓 **កម្មវិធីសិក្សា:** 500+ កម្មវិធី

🔍 **ជ្រើសរើសទីតាំងដើម្បីមើលបញ្ជីពេញលេញ:**"""

    keyboard = [
        [InlineKeyboardButton(f"📍 ភ្នំពេញ ({len(by_city['Phnom Penh'])} សាកលវិទ្យាល័យ)", callback_data="location_PP")],
        [InlineKeyboardButton(f"📍 សៀមរាប ({len(by_city['Siem Reap'])} សាកលវិទ្យាល័យ)", callback_data="location_SR")],
        [InlineKeyboardButton(f"📍 បាត់ដំបង ({len(by_city['Battambang'])} សាកលវិទ្យាល័យ)", callback_data="location_BTB")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]

    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_university_contact(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle university contact information request."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        # Extract university ID from callback data
        uni_id = query.data.split('_', 1)[1]

        # Get university data
        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)

        # Find university info
        uni_info = None
        for program in enhanced_data:
            if program.get('university_id') == uni_id:
                uni_info = program
                break

        if not uni_info:
            text = "❌ សូមអភ័យទោស! មិនអាចរកឃើញព័ត៌មានទំនាក់ទំនងសាកលវិទ្យាល័យ។"
            keyboard = [[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]]
        else:
            uni_name = uni_info.get('university_name_kh', 'មិនមានឈ្មោះ')
            city = uni_info.get('city', 'មិនមានទីតាំង')
            uni_type = uni_info.get('university_type', 'មិនមានទិន្នន័យ')
            address = uni_info.get('address', uni_info.get('location', 'មិនមានអាសយដ្ឋាន'))
            phone = uni_info.get('phone', 'មិនមានទិន្នន័យ')
            email = uni_info.get('email', 'មិនមានទិន្នន័យ')
            website = uni_info.get('website', 'មិនមានទិន្នន័យ')

            # Count programs for this university
            program_count = sum(1 for p in enhanced_data if p.get('university_id') == uni_id)

            text = f"""📍 **ព័ត៌មានទីតាំងសាកលវិទ្យាល័យ**

🏫 **ឈ្មោះ:** {uni_name}
🏛️ **ប្រភេទ:** {uni_type}
🏙️ **ទីក្រុង:** {city}
🏠 **អាសយដ្ឋានពេញលេញ:** {address}
🗺️ **ទីតាំងនៃសាកលវិទ្យាល័យ:** Google Maps

📚 **មុខជំនាញដែលមាន:** {program_count} មុខជំនាញ

📞 **ព័ត៌មានទំនាក់ទំនង:**
• 📞 ទូរស័ព្ទ: {phone}
• 📧 អ៊ីមែល: {email}
• 🌐 គេហទំព័រ: {website}"""

            keyboard = [
                [InlineKeyboardButton("📚 មើលកម្មវិធីសិក្សា", callback_data=f"programs_{uni_id}")],
                [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
            ]

        await safe_edit_message(
            query,
            text.strip(),
            InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error in handle_university_contact: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មាន។ សូមព្យាយាមម្តងទៀត។",
            InlineKeyboardMarkup([[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]])
        )

async def handle_university_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle university details view from location/major browsing."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        # Extract university ID from callback data (uni_details_<uni_id>)
        uni_id = query.data.split('_', 2)[2]

        # Get university data
        raw_data = load_raw()
        enhanced_data = add_derived_features(raw_data)

        # Find university info
        uni_info = None
        programs = []
        for program in enhanced_data:
            if program.get('university_id') == uni_id:
                if not uni_info:
                    uni_info = program
                programs.append(program)

        if not uni_info:
            text = "❌ សូមអភ័យទោស! មិនអាចរកឃើញព័ត៌មានសាកលវិទ្យាល័យ។"
            keyboard = [[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]]
        else:
            uni_name = uni_info.get('university_name_kh', 'មិនមានឈ្មោះ')
            city = uni_info.get('city', 'មិនមានទីតាំង')
            uni_type = uni_info.get('university_type', 'មិនមានទិន្នន័យ')
            address = uni_info.get('address', uni_info.get('location', 'មិនមានអាសយដ្ឋាន'))
            phone = uni_info.get('phone', 'មិនមានទិន្នន័យ')
            email = uni_info.get('email', 'មិនមានទិន្នន័យ')
            website = uni_info.get('website', 'មិនមានទិន្នន័យ')

            text = f"""🏛️ **{uni_name}**

📍 **ព័ត៌មានទូទៅ:**
• 🏙️ ទីតាំង: {city}
• 🏛️ ប្រភេទ: {uni_type}
• 🎓 កម្មវិធីសិក្សា: {len(programs)} កម្មវិធី
• 📍 អាសយដ្ឋាន: {address}

📞 **ព័ត៌មានទំនាក់ទំនង:**
• 📞 ទូរស័ព្ទ: {phone}
• 📧 អ៊ីមែល: {email}
• 🌐 គេហទំព័រ: {website}

🎯 **ជ្រើសរើសមុខងារ:**"""

            keyboard = [
                [
                    InlineKeyboardButton("📞 ទំនាក់ទំនង", callback_data=f"contact_{uni_id}"),
                    InlineKeyboardButton("📍 ទីតាំង", callback_data=f"location_{uni_id}")
                ],
                [
                    InlineKeyboardButton("📚 កម្មវិធីសិក្សា", callback_data=f"programs_{uni_id}"),
                    InlineKeyboardButton("📋 តម្រូវការចូលរៀន", callback_data=f"requirements_{uni_id}")
                ],
                [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
            ]

        await safe_edit_message(
            query,
            text.strip(),
            InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error in handle_university_details: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មាន។ សូមព្យាយាមម្តងទៀត។",
            InlineKeyboardMarkup([[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]])
        )

async def handle_select_for_comparison(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle university selection for comparison."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        # Get all universities
        universities = get_all_universities()

        if not universities:
            text = "❌ មិនអាចរកឃើញសាកលវិទ្យាល័យសម្រាប់ប្រៀបធៀប។"
            keyboard = [[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="compare_universities")]]
        else:
            text = f"""🏛️ **ជ្រើសរើសសាកលវិទ្យាល័យសម្រាប់ប្រៀបធៀប**

📊 **មានសាកលវិទ្យាល័យ {len(universities)} កន្លែង**

🎯 **ជ្រើសរើសសាកលវិទ្យាល័យទី 1:**"""

            keyboard = []
            # Show universities as buttons (limit to 10 for better UX)
            for uni_id, uni_data in list(universities.items())[:10]:
                button_text = f"🏛️ {uni_data['name_kh']}"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"compare_select_{uni_id}")])

            keyboard.append([InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="compare_universities")])

        await safe_edit_message(
            query,
            text.strip(),
            InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error in handle_select_for_comparison: {e}")
        await safe_edit_message(
            query,
            "❌ មានបញ្ហាក្នុងការបង្ហាញបញ្ជីសាកលវិទ្យាល័យ។ សូមព្យាយាមម្តងទៀត។",
            InlineKeyboardMarkup([[InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="compare_universities")]])
        )

async def handle_compare_by_location(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle comparison by location."""
    query = update.callback_query
    await safe_answer_callback(query)

    by_city = get_universities_by_city()

    text = f"""📍 **ប្រៀបធៀបសាកលវិទ្យាល័យតាមទីតាំង**

🏛️ **ជ្រើសរើសទីតាំងដើម្បីប្រៀបធៀប:**

🏙️ **ភ្នំពេញ:** {len(by_city['Phnom Penh'])} សាកលវិទ្យាល័យ
🏛️ **សៀមរាប:** {len(by_city['Siem Reap'])} សាកលវិទ្យាល័យ
🌾 **បាត់ដំបង:** {len(by_city['Battambang'])} សាកលវិទ្យាល័យ

🎯 **ជ្រើសរើសទីតាំង:**"""

    keyboard = [
        [InlineKeyboardButton(f"📍 ភ្នំពេញ ({len(by_city['Phnom Penh'])} សាកលវិទ្យាល័យ)", callback_data="compare_location_PP")],
        [InlineKeyboardButton(f"📍 សៀមរាប ({len(by_city['Siem Reap'])} សាកលវិទ្យាល័យ)", callback_data="compare_location_SR")],
        [InlineKeyboardButton(f"📍 បាត់ដំបង ({len(by_city['Battambang'])} សាកលវិទ្យាល័យ)", callback_data="compare_location_BTB")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="compare_universities")]
    ]

    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_back_to_main(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle back to main menu."""
    query = update.callback_query
    await safe_answer_callback(query)

    # Import the start command to show main menu
    from src.bot.commands_v3 import start_command

    # Create a fake message update for start command
    class FakeMessage:
        def __init__(self, query):
            self.query = query

        async def reply_text(self, text, reply_markup=None, parse_mode=None):
            await safe_edit_message(self.query, text, reply_markup, parse_mode)

    # Create fake update with message
    fake_update = type('Update', (), {})()
    fake_update.message = FakeMessage(query)

    # Call start command
    await start_command(fake_update, context)
