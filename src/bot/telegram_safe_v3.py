"""
EduGuideBot v3 Telegram Safe Operations
Provides safe wrappers for Telegram operations with error handling
"""

import logging
import json
from pathlib import Path
from telegram import Update, CallbackQuery
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TimedOut, NetworkError

logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
logs_dir = Path(__file__).parents[2] / "tests" / "logs"
logs_dir.mkdir(parents=True, exist_ok=True)
failure_log_path = logs_dir / "failures.log"


def log_telegram_errors(func):
    """Decorator to log Telegram errors."""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Telegram error in {func.__name__}: {e}")
            
            # Log to failure log
            error_data = {
                "function": func.__name__,
                "error": str(e),
                "error_type": type(e).__name__
            }
            
            with open(failure_log_path, "a", encoding="utf-8") as f:
                f.write(json.dumps(error_data, ensure_ascii=False) + "\n")
            
            # Return safe fallback message
            return "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
    
    return wrapper


async def safe_answer_callback(query: CallbackQuery, text: str = None) -> bool:
    """Safely answer callback query with Khmer fallback and comprehensive error handling."""
    try:
        if not query:
            logger.warning("Attempted to answer null callback query")
            return False

        if text:
            await query.answer(text)
        else:
            await query.answer("✅")  # Default success message
        return True
    except Exception as e:
        logger.error(f"Error answering callback: {e}")
        try:
            # Try fallback message
            await query.answer("❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។")
        except Exception as e2:
            logger.error(f"Fallback callback answer also failed: {e2}")
            pass
        return False


async def safe_edit_message(query: CallbackQuery, text: str, reply_markup=None, parse_mode=None) -> bool:
    """Safely edit message with Khmer fallback and comprehensive error handling."""
    try:
        if not query or not query.message:
            logger.warning("Attempted to edit message with null query or message")
            return False

        # Check if message actually needs editing to avoid unnecessary API calls
        current_text = query.message.text or ""
        current_markup = query.message.reply_markup

        if current_text == text and current_markup == reply_markup:
            logger.debug("Message content unchanged, skipping edit")
            return True

        # Try to edit the message
        await query.edit_message_text(
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
        return True

    except BadRequest as e:
        if "message is not modified" in str(e).lower():
            logger.debug("Message content unchanged, skipping edit")
            return True
        elif "message to edit not found" in str(e).lower():
            logger.warning("Message to edit not found, possibly deleted")
            return False
        elif "can't parse entities" in str(e).lower():
            logger.warning(f"Parse error, trying with plain text: {e}")
            # Retry without parse_mode
            try:
                await query.edit_message_text(text=text, reply_markup=reply_markup)
                return True
            except:
                pass
        else:
            logger.warning(f"BadRequest in safe_edit_message: {e}")

    except (TimedOut, NetworkError) as e:
        logger.warning(f"Network error in safe_edit_message: {e}")

    except Exception as e:
        logger.error(f"Unexpected error in safe_edit_message: {e}", exc_info=True)

    # Fallback: try to send a simple error message
    try:
        fallback_text = "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        await query.edit_message_text(fallback_text)
        return True
    except:
        logger.error("Even fallback message edit failed")
        return False


async def safe_send_message(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    text: str,
    reply_markup=None,
    parse_mode='HTML'
) -> bool:
    """
    Safely send a message with comprehensive error handling and fallback.

    Args:
        update: The update object
        context: The context object
        text: Message text to send
        reply_markup: Keyboard markup (optional)
        parse_mode: Parse mode for text formatting (default: 'HTML')

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if update.callback_query:
            await update.callback_query.message.reply_text(
                text=text,
                reply_markup=reply_markup,
                parse_mode=parse_mode
            )
        elif update.message:
            await update.message.reply_text(
                text=text,
                reply_markup=reply_markup,
                parse_mode=parse_mode
            )
        else:
            logger.error("No message or callback_query found in update")
            return False
        return True

    except BadRequest as e:
        if "can't parse entities" in str(e).lower():
            logger.warning(f"Parse error, trying with plain text: {e}")
            # Retry without parse_mode
            try:
                if update.callback_query:
                    await update.callback_query.message.reply_text(text=text, reply_markup=reply_markup)
                elif update.message:
                    await update.message.reply_text(text=text, reply_markup=reply_markup)
                return True
            except:
                pass
        else:
            logger.warning(f"BadRequest in safe_send_message: {e}")

    except (TimedOut, NetworkError) as e:
        logger.warning(f"Network error in safe_send_message: {e}")

    except Exception as e:
        logger.error(f"Unexpected error in safe_send_message: {e}", exc_info=True)

    # Fallback: try to send a simple error message
    try:
        fallback_text = "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        if update.callback_query:
            await update.callback_query.message.reply_text(fallback_text)
        elif update.message:
            await update.message.reply_text(fallback_text)
        return True
    except Exception:
        logger.error("Even fallback message send failed")
        return False
