"""Simple runtime i18n loader (Khmer only)."""
from importlib import resources
import json
import os
from functools import lru_cache
from pathlib import Path

_SUPPORTED = {"kh"}
_DEFAULT = "kh"

# Cache for loaded translations
_CACHE = {}


def get_lang(context_or_chat_data):
    """Get language from context or chat data, returns "kh" only."""
    # Always return Khmer since we only support Khmer now
    return _DEFAULT


def set_lang(chat_data, lang_code):
    """Validate and store language preference."""
    if lang_code in _SUPPORTED:
        chat_data["lang"] = lang_code
    return get_lang(chat_data)


@lru_cache(maxsize=2)  # Cache for both languages
def get_translation_bundle(lang):
    """Get translation bundle for specified language with caching."""
    if lang not in _SUPPORTED:
        lang = _DEFAULT

    try:
        # Try to load from package resources
        bundle_text = resources.read_text(__package__, f"{lang}.json")
        return json.loads(bundle_text)
    except (FileNotFoundError, json.JSONDecodeError):
        # Fallback: try to load from file system
        try:
            bundle_path = Path(__file__).parent / f"{lang}.json"
            with open(bundle_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # Ultimate fallback: empty bundle
            return {}


def lazy_load_bundle(lang):
    """Lazy load translation bundle for specified language."""
    if lang not in _SUPPORTED:
        lang = _DEFAULT

    # Load translation bundle if not cached
    if lang not in _CACHE:
        _CACHE[lang] = get_translation_bundle(lang)

    return _CACHE[lang]


def t(key, lang):
    """Translate string key to specified language."""
    if lang not in _SUPPORTED:
        lang = _DEFAULT

    # Lazy load bundle
    bundle = lazy_load_bundle(lang)
    return bundle.get(key, key)  # fallback: key itself


def clear_cache():
    """Clear translation cache (useful for testing)."""
    global _CACHE
    _CACHE = {}


def get_supported_languages():
    """Get list of supported language codes."""
    return list(_SUPPORTED)


def is_supported(lang_code):
    """Check if language code is supported."""
    return lang_code in _SUPPORTED
