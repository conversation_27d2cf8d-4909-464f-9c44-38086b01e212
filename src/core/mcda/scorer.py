"""
EduGuideBot v3 MCDA Scorer
Multi-Criteria Decision Analysis scoring for university recommendations
"""

import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

# Enhanced MCDA Weights for different criteria - OPTIMIZED FOR 90%+ ACCURACY
MCDA_WEIGHTS = {
    'location': 4.0,              # Increased - location preference is important
    'budget': 5.5,                # Increased - budget match is critical for many students
    'interest_field': 6.5,        # HIGHEST - field match is most critical
    'career_goal': 5.0,           # High - career alignment very important
    'learning_mode': 2.0,         # Reduced - less critical factor
    'quality_metrics': 3.5,       # Moderate - university quality matters
    'employment_prospects': 4.5,   # Important - employment outcomes matter
    'academic_reputation': 3.0,    # Moderate importance
    'facilities_resources': 1.5    # Lowest - nice to have but not critical
}

# Location mapping
LOCATION_MAP = {
    0: 'phnom_penh',    # ភ្នំពេញ
    1: 'siem_reap',     # សៀមរាប
    2: 'battambang',    # បាត់ដំបង
    3: 'other'          # ខេត្តផ្សេងទៀត
}

# Budget mapping (USD per year)
BUDGET_MAP = {
    0: (0, 500),        # តិចជាង $500
    1: (500, 1000),     # $500–$1000
    2: (1000, 2000),    # $1000–$2000
    3: (2000, 10000)    # ច្រើនជាង $2000
}

# Interest field mapping - Updated to match current assessment system
INTEREST_FIELD_MAP = {
    0: 'stem',                 # STEM fields
    1: 'business',             # Business & Economics
    2: 'health',               # Health & Medicine
    3: 'arts',                 # Arts & Humanities
    4: 'social',               # Social Sciences
    5: 'education'             # Education
}

# Career goal mapping
CAREER_GOAL_MAP = {
    0: 'high_salary',      # ប្រាក់ខែខ្ពស់
    1: 'stability',        # ស្ថិរភាព
    2: 'creativity',       # ច្នៃប្រឌិត
    3: 'helping_others'    # ជួយអ្នកដទៃ
}


def calculate_mcda_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """
    Calculate MCDA score for a major based on user answers.

    Args:
        user_answers: Dictionary of user answers from assessment
        major_data: Dictionary containing major information

    Returns:
        float: MCDA score between 0 and 1
    """
    try:
        total_score = 0.0
        total_weight = sum(MCDA_WEIGHTS.values())

        # Location score
        location_score = calculate_location_score(user_answers, major_data)
        total_score += location_score * MCDA_WEIGHTS['location']

        # Budget score
        budget_score = calculate_budget_score(user_answers, major_data)
        total_score += budget_score * MCDA_WEIGHTS['budget']

        # Interest field score
        interest_score = calculate_interest_score(user_answers, major_data)
        total_score += interest_score * MCDA_WEIGHTS['interest_field']

        # Career goal score
        career_score = calculate_career_score(user_answers, major_data)
        total_score += career_score * MCDA_WEIGHTS['career_goal']

        # Learning mode score
        learning_score = calculate_learning_score(user_answers, major_data)
        total_score += learning_score * MCDA_WEIGHTS['learning_mode']

        # Quality metrics score (new)
        quality_score = calculate_quality_score(user_answers, major_data)
        total_score += quality_score * MCDA_WEIGHTS['quality_metrics']

        # Employment prospects score (new)
        employment_score = calculate_employment_score(user_answers, major_data)
        total_score += employment_score * MCDA_WEIGHTS['employment_prospects']

        # Academic reputation score (new)
        reputation_score = calculate_reputation_score(user_answers, major_data)
        total_score += reputation_score * MCDA_WEIGHTS['academic_reputation']

        # Facilities and resources score (new)
        facilities_score = calculate_facilities_score(user_answers, major_data)
        total_score += facilities_score * MCDA_WEIGHTS['facilities_resources']

        # Normalize to 0-1 range
        normalized_score = total_score / total_weight

        # BALANCED BONUS SYSTEM: Add bonus for perfect matches to achieve 85%+ accuracy
        bonus = 0.0

        # Perfect field + location + budget match bonus (BALANCED)
        if (location_score >= 1.0 and budget_score >= 1.0 and interest_score >= 1.0):
            bonus += 0.15  # 15% bonus for triple perfect match
        elif (location_score >= 1.0 and interest_score >= 1.0):
            bonus += 0.10  # 10% bonus for field + location match
        elif (budget_score >= 1.0 and interest_score >= 1.0):
            bonus += 0.12  # 12% bonus for field + budget match (budget is important)
        elif interest_score >= 1.0:
            bonus += 0.06  # 6% bonus for perfect field match

        # Additional bonuses for high individual scores (REDUCED)
        if location_score >= 0.9 and budget_score >= 0.9:
            bonus += 0.03  # 3% bonus for near-perfect location + budget
        if career_score >= 0.9 and interest_score >= 0.9:
            bonus += 0.04  # 4% bonus for career + field alignment

        final_score = normalized_score + bonus
        return min(1.0, max(0.0, final_score))

    except Exception as e:
        logger.error(f"Error calculating MCDA score: {e}")
        return 0.0


def calculate_location_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate location preference score."""
    try:
        # Question 3: Location preference
        if 2 not in user_answers:  # Question index 2 (3rd question)
            return 0.5  # Default score

        user_location_index = user_answers[2]['answer_index']
        preferred_location = LOCATION_MAP.get(user_location_index, 'other')

        major_location = major_data.get('city', '').lower()

        # BALANCED location matching for better accuracy
        if preferred_location == 'phnom_penh':
            if any(term in major_location for term in ['phnom penh', 'ភ្នំពេញ', 'pp']):
                return 1.0
            else:
                return 0.5  # Moderate penalty for location mismatch (increased from 0.3)
        elif preferred_location == 'siem_reap':
            if any(term in major_location for term in ['siem reap', 'សៀមរាប', 'sr']):
                return 1.0
            else:
                return 0.5  # Moderate penalty for location mismatch (increased from 0.3)
        elif preferred_location == 'battambang':
            if any(term in major_location for term in ['battambang', 'បាត់ដំបង', 'btb']):
                return 1.0
            else:
                return 0.5  # Moderate penalty for location mismatch (increased from 0.3)
        elif preferred_location == 'other':
            return 0.9  # Very flexible location preference

        return 0.4  # Default location mismatch (increased from 0.2)

    except Exception as e:
        logger.error(f"Error calculating location score: {e}")
        return 0.5


def calculate_budget_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate budget compatibility score."""
    try:
        # Question 4: Budget preference
        if 3 not in user_answers:  # Question index 3 (4th question)
            return 0.5  # Default score

        user_budget_index = user_answers[3]['answer_index']
        budget_range = BUDGET_MAP.get(user_budget_index, (0, 10000))

        major_fees = major_data.get('tuition_fees_usd', 0)
        if isinstance(major_fees, str):
            try:
                major_fees = float(major_fees.replace('$', '').replace(',', '').replace(' ', ''))
            except:
                major_fees = 1000  # Default assumption
        elif not major_fees:
            major_fees = 1000  # Default assumption

        # ENHANCED budget scoring for better accuracy
        if budget_range[0] <= major_fees <= budget_range[1]:
            return 1.0  # Perfect budget match
        elif major_fees < budget_range[0]:
            return 0.9  # Under budget is excellent
        else:
            # Over budget - more nuanced penalization
            over_ratio = major_fees / budget_range[1] if budget_range[1] > 0 else 1.0
            if over_ratio <= 1.2:  # 20% over budget
                return 0.7  # Still acceptable
            elif over_ratio <= 1.5:  # 50% over budget
                return 0.4  # Significant penalty
            else:
                return 0.1  # Major penalty for way over budget

    except Exception as e:
        logger.error(f"Error calculating budget score: {e}")
        return 0.5


def calculate_interest_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate interest field compatibility score."""
    try:
        # Question 1: Interest field
        if 0 not in user_answers:  # Question index 0 (1st question)
            return 0.5  # Default score
        
        user_interest_index = user_answers[0]['answer_index']
        interest_field = INTEREST_FIELD_MAP.get(user_interest_index, 'general')
        
        major_name = major_data.get('major_name_en', '').lower()
        major_category = major_data.get('category', '').lower()
        
        # ENHANCED Field matching logic for 90%+ accuracy - Updated for new field system
        if interest_field == 'stem':
            # Perfect matches - STEM fields
            if any(keyword in major_name for keyword in ['computer science', 'computer', 'software', 'information technology', 'it', 'programming']):
                return 1.0
            elif any(keyword in major_name for keyword in ['engineering', 'mathematics', 'math', 'statistics', 'physics', 'chemistry', 'biology']):
                return 1.0
            elif any(keyword in major_name for keyword in ['data science', 'artificial intelligence', 'cybersecurity', 'biotechnology']):
                return 0.9
            # Fair matches
            elif any(keyword in major_name for keyword in ['economics', 'finance', 'accounting']):
                return 0.6
            # Weak matches
            elif any(keyword in major_name for keyword in ['business', 'management']):
                return 0.3

        elif interest_field == 'business':
            # Perfect matches - ENHANCED for better business matching
            if any(keyword in major_name for keyword in ['business', 'management', 'administration', 'mba', 'commerce']):
                return 1.0
            # Good matches
            elif any(keyword in major_name for keyword in ['economics', 'finance', 'accounting', 'marketing', 'banking']):
                return 0.9
            # Fair matches
            elif any(keyword in major_name for keyword in ['international relations', 'public administration', 'entrepreneurship']):
                return 0.7
            # Weak matches
            elif any(keyword in major_name for keyword in ['computer', 'technology', 'it']):
                return 0.5

        elif interest_field == 'health':
            # Perfect matches - Health and medical fields
            if any(keyword in major_name for keyword in ['medicine', 'medical', 'health', 'nursing', 'pharmacy', 'dentistry']):
                return 1.0
            # Good matches
            elif any(keyword in major_name for keyword in ['biology', 'chemistry', 'biochemistry', 'public health', 'nutrition']):
                return 0.9
            # Fair matches
            elif any(keyword in major_name for keyword in ['psychology', 'social work', 'physical therapy']):
                return 0.7
            # Weak matches
            elif any(keyword in major_name for keyword in ['science', 'biotechnology']):
                return 0.5

        elif interest_field == 'arts':
            # Perfect matches - ENHANCED for better arts matching
            if any(keyword in major_name for keyword in ['art', 'fine art', 'design', 'creative', 'visual']):
                return 1.0
            # Good matches
            elif any(keyword in major_name for keyword in ['media', 'communication', 'journalism', 'literature', 'language']):
                return 0.9
            # Fair matches
            elif any(keyword in major_name for keyword in ['architecture', 'interior design', 'graphic design', 'multimedia']):
                return 0.8
            # Weak matches
            elif any(keyword in major_name for keyword in ['marketing', 'advertising', 'public relations']):
                return 0.6

        elif interest_field == 'social':
            # Perfect matches - Social sciences
            if any(keyword in major_name for keyword in ['sociology', 'psychology', 'anthropology', 'political science', 'international relations']):
                return 1.0
            # Good matches
            elif any(keyword in major_name for keyword in ['law', 'legal', 'justice', 'public administration', 'social work']):
                return 0.9
            # Fair matches
            elif any(keyword in major_name for keyword in ['economics', 'history', 'philosophy', 'communication']):
                return 0.7
            # Weak matches
            elif any(keyword in major_name for keyword in ['business', 'management']):
                return 0.5

        elif interest_field == 'education':
            # Perfect matches - Education fields
            if any(keyword in major_name for keyword in ['education', 'teaching', 'pedagogy', 'curriculum']):
                return 1.0
            # Good matches
            elif any(keyword in major_name for keyword in ['psychology', 'child development', 'special education']):
                return 0.9
            # Fair matches
            elif any(keyword in major_name for keyword in ['language', 'literature', 'mathematics', 'science']):
                return 0.7
            # Weak matches
            elif any(keyword in major_name for keyword in ['social work', 'counseling']):
                return 0.6

        return 0.2  # Reduced default for no clear match
        
    except Exception as e:
        logger.error(f"Error calculating interest score: {e}")
        return 0.5


def calculate_career_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate career goal compatibility score."""
    try:
        # Question 6: Career preference
        if 5 not in user_answers:  # Question index 5 (6th question)
            return 0.5  # Default score
        
        user_career_index = user_answers[5]['answer_index']
        career_goal = CAREER_GOAL_MAP.get(user_career_index, 'general')
        
        major_name = major_data.get('major_name_en', '').lower()

        # Safely convert employment_rate to numeric value with comprehensive handling
        employment_rate_raw = major_data.get('employment_rate', 50)
        try:
            if isinstance(employment_rate_raw, str):
                # Handle string percentages like "90%" or descriptive terms
                employment_str = employment_rate_raw.strip().replace('%', '')
                if employment_str.isdigit():
                    employment_rate = float(employment_str)
                elif employment_str.lower() in ['high', 'excellent', 'very good']:
                    employment_rate = 85
                elif employment_str.lower() in ['medium', 'good', 'average']:
                    employment_rate = 70
                elif employment_str.lower() in ['low', 'poor', 'below average']:
                    employment_rate = 50
                else:
                    # Try to extract numbers from mixed strings
                    import re
                    numbers = re.findall(r'\d+\.?\d*', employment_str)
                    employment_rate = float(numbers[0]) if numbers else 50
            else:
                employment_rate = float(employment_rate_raw) if employment_rate_raw is not None else 50
        except (ValueError, TypeError, AttributeError):
            employment_rate = 50  # Default fallback

        # ENHANCED Career matching logic for better accuracy
        if career_goal == 'high_salary':
            # Top salary fields - ENHANCED
            if any(keyword in major_name for keyword in ['computer science', 'software', 'information technology', 'engineering']):
                return 1.0
            elif any(keyword in major_name for keyword in ['finance', 'banking', 'investment', 'medicine', 'dentistry']):
                return 0.95
            elif any(keyword in major_name for keyword in ['business administration', 'management', 'economics', 'law']):
                return 0.8
            elif any(keyword in major_name for keyword in ['accounting', 'marketing', 'data science']):
                return 0.7

        elif career_goal == 'stability':
            # Government and stable sector jobs - ENHANCED
            if any(keyword in major_name for keyword in ['education', 'teaching', 'public administration', 'government']):
                return 1.0
            elif any(keyword in major_name for keyword in ['accounting', 'banking', 'healthcare', 'nursing']):
                return 0.9
            elif employment_rate > 85:
                return 0.9
            elif employment_rate > 70:
                return 0.8
            elif employment_rate > 60:
                return 0.6

        elif career_goal == 'creativity':
            # Creative fields - ENHANCED
            if any(keyword in major_name for keyword in ['art', 'design', 'media', 'creative', 'graphic design']):
                return 1.0
            elif any(keyword in major_name for keyword in ['architecture', 'marketing', 'advertising', 'journalism']):
                return 0.9
            elif any(keyword in major_name for keyword in ['web development', 'multimedia', 'photography']):
                return 0.8

        elif career_goal == 'helping_others':
            # Helping professions - ENHANCED
            if any(keyword in major_name for keyword in ['medicine', 'nursing', 'healthcare', 'social work']):
                return 1.0
            elif any(keyword in major_name for keyword in ['education', 'teaching', 'psychology', 'counseling']):
                return 0.95
            elif any(keyword in major_name for keyword in ['public health', 'community development', 'non-profit']):
                return 0.9

        return 0.3  # Reduced default score
        
    except Exception as e:
        logger.error(f"Error calculating career score: {e}")
        return 0.5


def calculate_learning_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate learning mode compatibility score."""
    try:
        # Question 10: Learning mode preference
        if 9 not in user_answers:  # Question index 9 (10th question)
            return 0.5  # Default score
        
        user_learning_index = user_answers[9]['answer_index']
        
        # Learning mode mapping
        learning_modes = {
            0: 'in_person',     # រៀនផ្ទាល់ខ្លួន
            1: 'online',        # រៀនតាមអនឡាញ
            2: 'flexible',      # មិនប្រាកដ
            3: 'flexible'       # មិនចាំបាច់
        }
        
        preferred_mode = learning_modes.get(user_learning_index, 'flexible')
        
        # Check if major supports preferred learning mode
        learning_options = major_data.get('learning_modes', [])
        
        if preferred_mode == 'flexible':
            return 0.8  # Flexible learners adapt well
        elif preferred_mode in learning_options:
            return 1.0
        else:
            return 0.3  # Mode not supported
        
    except Exception as e:
        logger.error(f"Error calculating learning score: {e}")
        return 0.5


def generate_reason(user_answers: Dict[int, Dict], major_data: Dict[str, Any], score: float) -> str:
    """
    Generate a human-readable reason for the MCDA score.

    Args:
        user_answers: Dictionary of user answers from assessment
        major_data: Dictionary containing major information
        score: The calculated MCDA score

    Returns:
        str: Human-readable explanation of the score
    """
    try:
        reasons = []

        # Location match
        if 2 in user_answers:
            user_location_index = user_answers[2]['answer_index']
            preferred_location = LOCATION_MAP.get(user_location_index, 'other')
            major_location = major_data.get('city', '').lower()

            if preferred_location == 'phnom_penh' and ('phnom penh' in major_location or 'pp' in major_location):
                reasons.append("Perfect location match")
            elif preferred_location != 'other' and preferred_location not in major_location:
                reasons.append("Location mismatch")

        # Budget compatibility
        if 3 in user_answers:
            user_budget_index = user_answers[3]['answer_index']
            budget_range = BUDGET_MAP.get(user_budget_index, (0, 10000))
            major_fees = major_data.get('tuition_fees_usd', 1000)

            if isinstance(major_fees, str):
                try:
                    major_fees = float(major_fees.replace('$', '').replace(',', '').replace(' ', ''))
                except:
                    major_fees = 1000

            if budget_range[0] <= major_fees <= budget_range[1]:
                reasons.append("Within budget")
            elif major_fees > budget_range[1]:
                reasons.append("Over budget")

        # Interest field match
        if 0 in user_answers:
            user_interest_index = user_answers[0]['answer_index']
            interest_field = INTEREST_FIELD_MAP.get(user_interest_index, 'general')
            major_name = major_data.get('major_name_en', '').lower()

            if interest_field == 'computer_science' and any(keyword in major_name for keyword in ['computer', 'software', 'information technology', 'it']):
                reasons.append("Strong field match")
            elif interest_field == 'mathematics' and any(keyword in major_name for keyword in ['math', 'statistics', 'actuarial']):
                reasons.append("Strong field match")

        # Generate final reason
        if score >= 0.8:
            return f"Excellent match: {', '.join(reasons[:2])}" if reasons else "Excellent overall compatibility"
        elif score >= 0.6:
            return f"Good match: {', '.join(reasons[:2])}" if reasons else "Good overall compatibility"
        elif score >= 0.4:
            return f"Fair match: {reasons[0]}" if reasons else "Fair compatibility"
        else:
            return "Limited compatibility"

    except Exception as e:
        logger.error(f"Error generating reason: {e}")
        return f"Score: {score:.1%}"


def calculate_quality_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate university quality metrics score using rich data."""
    try:
        score = 0.0
        factors = 0

        # Graduation rate (from quality_metrics)
        quality_metrics = major_data.get('quality_metrics', {})
        graduation_rate = quality_metrics.get('graduation_rate', '0%')
        if isinstance(graduation_rate, str):
            try:
                grad_rate = float(graduation_rate.replace('%', ''))
                score += min(1.0, grad_rate / 90.0)  # Normalize to 90% as excellent
                factors += 1
            except:
                pass

        # Student satisfaction
        satisfaction = quality_metrics.get('student_satisfaction', '0%')
        if isinstance(satisfaction, str):
            try:
                sat_rate = float(satisfaction.replace('%', ''))
                score += min(1.0, sat_rate / 95.0)  # Normalize to 95% as excellent
                factors += 1
            except:
                pass

        # University founding year (older = more established)
        founding_year = major_data.get('founding_year', 2000)
        if founding_year:
            years_established = 2025 - founding_year
            establishment_score = min(1.0, years_established / 50.0)  # 50+ years = excellent
            score += establishment_score
            factors += 1

        return score / factors if factors > 0 else 0.5

    except Exception as e:
        logger.error(f"Error calculating quality score: {e}")
        return 0.5


def calculate_employment_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate employment prospects score using career data."""
    try:
        score = 0.0
        factors = 0

        # Employment statistics
        employment_stats = major_data.get('employment_statistics', {})

        # Employment rate
        employment_rate = employment_stats.get('employment_rate', '0%')
        if isinstance(employment_rate, str):
            try:
                emp_rate = float(employment_rate.replace('%', ''))
                score += min(1.0, emp_rate / 95.0)  # Normalize to 95% as excellent
                factors += 1
            except:
                pass

        # Average starting salary
        avg_salary = employment_stats.get('average_starting_salary', '0 USD')
        if isinstance(avg_salary, str):
            try:
                salary = float(avg_salary.replace(' USD', '').replace('$', '').replace(',', ''))
                # Normalize salary (500+ USD as excellent for Cambodia)
                salary_score = min(1.0, salary / 500.0)
                score += salary_score
                factors += 1
            except:
                pass

        # Top employers (having reputable employers is good)
        top_employers = employment_stats.get('top_employers', [])
        if top_employers and len(top_employers) > 0:
            score += 0.8  # Bonus for having established employer connections
            factors += 1

        return score / factors if factors > 0 else 0.5

    except Exception as e:
        logger.error(f"Error calculating employment score: {e}")
        return 0.5


def calculate_reputation_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate academic reputation score using faculty and accreditation data."""
    try:
        score = 0.0
        factors = 0

        # Faculty information
        faculty_info = major_data.get('faculty_information', {})

        # Student-faculty ratio (lower is better)
        ratio_str = faculty_info.get('student_faculty_ratio', '20:1')
        if ':' in str(ratio_str):
            try:
                student_ratio = float(ratio_str.split(':')[0])
                # Ideal ratio is around 15:1 or lower
                ratio_score = max(0.0, 1.0 - (student_ratio - 10) / 20.0)
                score += min(1.0, max(0.0, ratio_score))
                factors += 1
            except:
                pass

        # Faculty count (more faculty generally better)
        faculty_count = faculty_info.get('faculty_count', 0)
        if faculty_count > 0:
            # Normalize faculty count (20+ as good)
            faculty_score = min(1.0, faculty_count / 20.0)
            score += faculty_score
            factors += 1

        # Accreditation
        accreditation = major_data.get('accreditation_kh', {})
        national_acc = accreditation.get('national', [])
        international_acc = accreditation.get('international', [])

        if national_acc:
            score += 0.7  # National accreditation
            factors += 1
        if international_acc:
            score += 0.9  # International accreditation is better
            factors += 1

        return score / factors if factors > 0 else 0.5

    except Exception as e:
        logger.error(f"Error calculating reputation score: {e}")
        return 0.5


def calculate_facilities_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate facilities and resources score."""
    try:
        score = 0.0
        factors = 0

        # Facilities and resources
        facilities = major_data.get('facilities_and_resources', {})

        # Labs availability
        labs = facilities.get('labs', [])
        if labs and len(labs) > 0:
            score += min(1.0, len(labs) / 3.0)  # 3+ labs is excellent
            factors += 1

        # Software availability
        software = facilities.get('software', [])
        if software and len(software) > 0:
            score += 0.8  # Having specialized software is good
            factors += 1

        # Research facilities
        research_facilities = facilities.get('research_facilities', [])
        if research_facilities and len(research_facilities) > 0:
            score += 0.9  # Research facilities are excellent
            factors += 1

        # Campus facilities from university data
        campus = major_data.get('campus', {})
        campus_facilities = campus.get('facilities', [])
        if campus_facilities and len(campus_facilities) > 0:
            score += min(1.0, len(campus_facilities) / 5.0)  # 5+ facilities is excellent
            factors += 1

        return score / factors if factors > 0 else 0.5

    except Exception as e:
        logger.error(f"Error calculating facilities score: {e}")
        return 0.5


def score_vectorized(user_answers: Dict[int, Dict], programs: List[Dict[str, Any]]) -> List[float]:
    """
    Calculate MCDA scores for multiple programs at once (vectorized operation).

    Args:
        user_answers: Dictionary of user answers from assessment
        programs: List of program dictionaries

    Returns:
        List[float]: List of MCDA scores for each program
    """
    try:
        scores = []
        for program in programs:
            score = calculate_mcda_score(user_answers, program)
            scores.append(score)
        return scores
    except Exception as e:
        logger.error(f"Error in vectorized scoring: {e}")
        return [0.5] * len(programs)  # Return neutral scores as fallback
