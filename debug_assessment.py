#!/usr/bin/env python3
"""
Debug the exact issue in complete_assessment
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def debug_complete_assessment():
    """Debug the complete_assessment function step by step"""
    print("🔍 Debugging complete_assessment function...")
    
    try:
        # Step 1: Test recommender
        print("Step 1: Testing recommender...")
        from src.core.recommender import get_recommendations
        
        assessment = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},
            1: {'answer_index': 1, 'answer_text': 'Medium'},
            2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
        }
        
        recommendations = await get_recommendations(assessment)
        print(f"✅ Recommender works: {len(recommendations)} recommendations")
        
        if not recommendations:
            print("❌ No recommendations - this is the problem!")
            return False
            
        # Step 2: Test recommendation structure
        print("Step 2: Testing recommendation structure...")
        rec = recommendations[0]
        
        # Test the exact fields used in complete_assessment
        name_kh = rec.get('major_name_kh', rec.get('major_name_en', 'មិនមានឈ្មោះ'))
        uni_kh = rec.get('university_name_kh', rec.get('university_name_en', 'មិនមានឈ្មោះ'))
        location = rec.get('city', rec.get('location', 'មិនមានទីតាំង'))
        fees = rec.get('tuition_fees_usd', rec.get('fees_usd', 'N/A'))
        hybrid_score = rec.get('hybrid_score', rec.get('score', 0.5))
        
        print(f"✅ name_kh: {name_kh}")
        print(f"✅ uni_kh: {uni_kh}")
        print(f"✅ location: {location}")
        print(f"✅ fees: {fees}")
        print(f"✅ hybrid_score: {hybrid_score}")
        
        # Step 3: Test confidence stars function
        print("Step 3: Testing confidence stars...")
        from src.bot.handlers.assessment import get_confidence_stars
        confidence = get_confidence_stars(hybrid_score)
        print(f"✅ confidence: {confidence}")
        
        # Step 4: Test message building
        print("Step 4: Testing message building...")
        rec_text = "🎯 ការណែនាំសម្រាប់អ្នក\n\n"
        rec_text += "ខាងក្រោមនេះជាមុខជំនាញដែលសមស្របបំផុតសម្រាប់អ្នក:\n\n"
        
        for i, rec in enumerate(recommendations[:5], 1):
            name_kh = rec.get('major_name_kh', rec.get('major_name_en', 'មិនមានឈ្មោះ'))
            uni_kh = rec.get('university_name_kh', rec.get('university_name_en', 'មិនមានឈ្មោះ'))
            location = rec.get('city', rec.get('location', 'មិនមានទីតាំង'))
            fees = rec.get('tuition_fees_usd', rec.get('fees_usd', 'N/A'))
            hybrid_score = rec.get('hybrid_score', rec.get('score', 0.5))
            confidence = get_confidence_stars(hybrid_score)
            
            rec_text += f"{i}. {name_kh}\n"
            rec_text += f"🏫 {uni_kh}\n"
            rec_text += f"📍 {location}\n"
            if fees != 'N/A':
                rec_text += f"💰 ${fees} USD\n"
            rec_text += f"★ {confidence} ({hybrid_score * 100:.0f}% ត្រូវគ្នា)\n\n"
        
        print(f"✅ Message built successfully: {len(rec_text)} characters")
        print(f"📝 Message preview: {rec_text[:200]}...")
        
        # Step 5: Test keyboard building
        print("Step 5: Testing keyboard building...")
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard_buttons = []
        for i in range(min(5, len(recommendations))):
            keyboard_buttons.append([
                InlineKeyboardButton(f"🔍 ព័ត៌មានបន្ថែម #{i+1}", callback_data=f"details_{i}")
            ])

        keyboard_buttons.append([
            InlineKeyboardButton("🔄 ធ្វើតេស្តម្តងទៀត", callback_data="restart_assessment")
        ])

        keyboard = InlineKeyboardMarkup(keyboard_buttons)
        print(f"✅ Keyboard built successfully: {len(keyboard_buttons)} buttons")
        
        # Step 6: Test bot_stats import
        print("Step 6: Testing bot_stats import...")
        from src.bot.commands_v3 import bot_stats
        print(f"✅ bot_stats imported: {bot_stats}")
        
        print("\n🎉 All steps passed! The function should work.")
        return True
        
    except Exception as e:
        print(f"❌ Error in step: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_with_mock_telegram():
    """Test complete_assessment with mock Telegram objects"""
    print("\n🤖 Testing with mock Telegram objects...")
    
    try:
        from src.bot.handlers.assessment import complete_assessment
        from unittest.mock import AsyncMock, MagicMock
        from telegram import CallbackQuery, Message, Chat, User
        from telegram.ext import ContextTypes
        
        # Create mock objects
        chat = MagicMock(spec=Chat)
        chat.id = 123456789
        
        user = MagicMock(spec=User)
        user.id = 123456789
        
        message = MagicMock(spec=Message)
        message.chat = chat
        message.from_user = user
        message.text = "old text"
        message.reply_markup = None
        
        query = AsyncMock(spec=CallbackQuery)
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.message = message
        query.from_user = user
        query.data = "ans_15_2"
        
        context = AsyncMock(spec=ContextTypes.DEFAULT_TYPE)
        context.user_data = {
            'current_question': 15,
            'answers': {
                0: {'answer_index': 2, 'answer_text': 'Computer Science'},
                1: {'answer_index': 1, 'answer_text': 'Medium'},
                2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
            },
            'lang': 'kh'
        }
        
        print("📞 Calling complete_assessment...")
        result = await complete_assessment(query, context)
        
        print(f"✅ Function completed: {result}")
        
        if query.edit_message_text.called:
            call_args = query.edit_message_text.call_args
            if call_args and call_args[0]:
                message_text = call_args[0][0]
                print(f"📝 Message sent: {message_text[:100]}...")
                return True
            else:
                print("❌ No message content")
                return False
        else:
            print("❌ edit_message_text not called")
            return False
            
    except Exception as e:
        print(f"❌ Mock test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run debug tests"""
    print("🚀 Debugging Assessment Completion Issue\n")
    
    test1 = await debug_complete_assessment()
    test2 = await test_with_mock_telegram()
    
    print(f"\n📊 Debug Results:")
    print(f"   Step-by-step test: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   Mock Telegram test: {'✅ PASS' if test2 else '❌ FAIL'}")
    
    if test1 and test2:
        print("\n🎉 Assessment function should be working!")
        print("✅ Try the bot again - it should work now!")
    else:
        print("\n❌ There's still an issue with the assessment function.")
    
    return test1 and test2


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
