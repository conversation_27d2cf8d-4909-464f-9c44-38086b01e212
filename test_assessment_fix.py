#!/usr/bin/env python3
"""
Test the assessment completion fix
"""

import sys
import asyncio
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_complete_assessment():
    """Test the complete_assessment function with real data"""
    print("🧪 Testing complete_assessment function...")
    
    try:
        from src.bot.handlers.assessment import complete_assessment
        from src.core.recommender import get_recommendations
        from telegram import CallbackQuery, Message, Chat, User
        from telegram.ext import ContextTypes
        
        # Create mock objects
        chat = MagicMock(spec=Chat)
        chat.id = 123456789
        
        user = MagicMock(spec=User)
        user.id = 123456789
        
        message = MagicMock(spec=Message)
        message.chat = chat
        message.from_user = user
        message.text = "old text"
        message.reply_markup = None
        
        query = AsyncMock(spec=CallbackQuery)
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.message = message
        query.from_user = user
        query.data = "ans_15_2"  # Last question answer
        
        context = AsyncMock(spec=ContextTypes.DEFAULT_TYPE)
        context.user_data = {
            'current_question': 15,
            'answers': {
                0: {'answer_index': 2, 'answer_text': 'Computer Science'},
                1: {'answer_index': 1, 'answer_text': 'Medium'},
                2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
                3: {'answer_index': 1, 'answer_text': 'Good'},
                4: {'answer_index': 0, 'answer_text': 'Career'},
                5: {'answer_index': 1, 'answer_text': 'Practical'},
                6: {'answer_index': 0, 'answer_text': 'Full-time'},
                7: {'answer_index': 1, 'answer_text': 'English'},
                8: {'answer_index': 0, 'answer_text': 'Technology'},
                9: {'answer_index': 1, 'answer_text': 'Innovation'},
                10: {'answer_index': 0, 'answer_text': 'Individual'},
                11: {'answer_index': 1, 'answer_text': 'Problem-solving'},
                12: {'answer_index': 0, 'answer_text': 'Private sector'},
                13: {'answer_index': 1, 'answer_text': 'Leadership'},
                14: {'answer_index': 0, 'answer_text': 'Research'},
                15: {'answer_index': 2, 'answer_text': 'Final answer'}
            },
            'lang': 'kh'
        }
        
        print("📊 Testing with sample assessment data...")
        
        # First, test the recommender directly
        assessment_data = context.user_data['answers']
        recommendations = await get_recommendations(assessment_data)
        
        if not recommendations:
            print("❌ No recommendations generated")
            return False
            
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        # Check recommendation structure
        sample_rec = recommendations[0]
        print(f"📋 Sample recommendation structure:")
        for key in ['major_name_kh', 'university_name_kh', 'city', 'tuition_fees_usd', 'hybrid_score']:
            value = sample_rec.get(key, 'MISSING')
            print(f"   {key}: {value}")
        
        # Now test the complete_assessment function
        print("\n🎯 Testing complete_assessment function...")
        
        result = await complete_assessment(query, context)
        
        # Check if edit_message_text was called (means function completed)
        if query.edit_message_text.called:
            print("✅ complete_assessment completed successfully")
            
            # Check the message content
            call_args = query.edit_message_text.call_args
            if call_args:
                message_text = call_args[0][0] if call_args[0] else ""
                print(f"📝 Generated message: {message_text[:200]}...")
                if "ការណែនាំសម្រាប់អ្នក" in message_text or "អនុសាសន៍" in message_text:
                    print("✅ Message contains expected Khmer content")
                    return True
                else:
                    print(f"❌ Message doesn't contain expected content")
                    return False
            else:
                print("❌ No message content found")
                return False
        else:
            print("❌ complete_assessment did not call edit_message_text")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_recommendation_data_structure():
    """Test that recommendations have the expected structure"""
    print("\n🔍 Testing recommendation data structure...")
    
    try:
        from src.core.recommender import get_recommendations
        
        # Sample assessment
        assessment = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},
            1: {'answer_index': 1, 'answer_text': 'Medium'},
            2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},
        }
        
        recommendations = await get_recommendations(assessment)
        
        if not recommendations:
            print("❌ No recommendations generated")
            return False
            
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        # Check required fields
        required_fields = [
            'major_name_kh', 'major_name_en',
            'university_name_kh', 'university_name_en',
            'city', 'location',
            'tuition_fees_usd', 'fees_usd',
            'hybrid_score', 'score'
        ]
        
        sample = recommendations[0]
        missing_fields = []
        
        for field in required_fields:
            if field not in sample:
                missing_fields.append(field)
                
        if missing_fields:
            print(f"❌ Missing fields: {missing_fields}")
            print(f"📋 Available fields: {list(sample.keys())}")
            return False
        else:
            print("✅ All required fields present")
            return True
            
    except Exception as e:
        print(f"❌ Data structure test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Testing Assessment Completion Fix\n")
    
    # Test 1: Recommendation data structure
    test1 = await test_recommendation_data_structure()
    
    # Test 2: Complete assessment function
    test2 = await test_complete_assessment()
    
    print(f"\n📊 Test Results:")
    print(f"   Recommendation Data Structure: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   Complete Assessment Function: {'✅ PASS' if test2 else '❌ FAIL'}")
    
    overall_pass = test1 and test2
    print(f"\n🎯 Overall Status: {'✅ ASSESSMENT FIX WORKING' if overall_pass else '❌ ASSESSMENT FIX FAILED'}")
    
    if overall_pass:
        print("\n🎉 The assessment completion is now working!")
        print("✅ Users can complete 16 questions and get recommendations!")
    else:
        print("\n⚠️  Assessment completion still has issues.")
    
    return overall_pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
