#!/usr/bin/env python3
"""
Test script to verify the recommendations system works with the fixed UI
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_recommendations_flow():
    """Test the complete recommendations flow"""
    print("🔄 Testing recommendations flow...")
    
    try:
        # Import the fixed modules
        from src.core.recommender import get_recommendations
        from src.bot.ui import create_enhanced_recommendations_view
        
        # Create sample user assessment
        sample_assessment = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},  # Interest field
            1: {'answer_index': 1, 'answer_text': 'Medium'},            # Budget
            2: {'answer_index': 0, 'answer_text': 'Phnom Penh'},        # Location
            'lang': 'kh'
        }
        
        print("📊 Generating recommendations...")
        recommendations = await get_recommendations(sample_assessment)
        
        if not recommendations:
            print("❌ No recommendations generated")
            return False
            
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        # Test the UI creation
        print("🎨 Testing UI creation...")
        message_text, reply_markup = create_enhanced_recommendations_view(recommendations, 'kh')
        
        if not message_text or not reply_markup:
            print("❌ UI creation failed")
            return False
            
        print("✅ UI created successfully")
        print(f"📝 Message length: {len(message_text)} characters")
        print(f"🔘 Buttons: {len(reply_markup.inline_keyboard)} rows")
        
        # Print first recommendation for verification
        if recommendations:
            rec = recommendations[0]
            print(f"\n📋 Sample recommendation:")
            print(f"   Major ID: {rec.get('major_id', 'N/A')}")
            print(f"   Major Name: {rec.get('major_name', 'N/A')}")
            print(f"   University ID: {rec.get('university_id', 'N/A')}")
            print(f"   University Name: {rec.get('university_name', 'N/A')}")
            print(f"   Location: {rec.get('location', 'N/A')}")
            print(f"   Fees USD: {rec.get('fees_usd', 'N/A')}")
            print(f"   Employment Rate: {rec.get('employment_rate', 'N/A')}")
            print(f"   Hybrid Score: {rec.get('score', 'N/A')}")
        
        # Test button callback data
        print(f"\n🔘 Testing button callback data...")
        for i, row in enumerate(reply_markup.inline_keyboard):
            for j, button in enumerate(row):
                callback_data = button.callback_data
                print(f"   Button [{i},{j}]: {button.text} -> {callback_data}")
                
                # Verify callback data is valid
                if len(callback_data.encode('utf-8')) > 64:
                    print(f"   ⚠️  Callback data too long: {len(callback_data.encode('utf-8'))} bytes")
                    
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_structure():
    """Test the data structure compatibility"""
    print("\n🔍 Testing data structure compatibility...")
    
    try:
        from src.core.data.loader import load_programs
        from src.core.data_loader import load_raw
        
        # Test both data loaders
        print("📂 Testing src.core.data.loader...")
        try:
            programs1 = load_programs()
            print(f"   ✅ Loaded {len(programs1)} programs")
            if programs1:
                sample = programs1[0]
                print(f"   📋 Sample fields: {list(sample.keys())[:10]}...")
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            
        print("📂 Testing src.core.data_loader...")
        try:
            programs2 = load_raw()
            print(f"   ✅ Loaded {len(programs2)} programs")
            if programs2:
                sample = programs2[0]
                print(f"   📋 Sample fields: {list(sample.keys())[:10]}...")
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Data structure test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting EduGuideBot recommendations fix test\n")
    
    # Test data structure compatibility
    data_test = test_data_structure()
    
    # Test recommendations flow
    rec_test = await test_recommendations_flow()
    
    print(f"\n📊 Test Results:")
    print(f"   Data Structure: {'✅ PASS' if data_test else '❌ FAIL'}")
    print(f"   Recommendations: {'✅ PASS' if rec_test else '❌ FAIL'}")
    
    if data_test and rec_test:
        print("\n🎉 All tests passed! The recommendations system should work now.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
