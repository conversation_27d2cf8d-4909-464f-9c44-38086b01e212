#!/usr/bin/env python3
"""
Test Critical Fixes for EduGuideBot v3
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_university_handlers():
    """Test university contact, location, and other majors handlers"""
    print("🔧 Testing University Handlers...")
    
    try:
        from src.bot.handlers.details import (
            show_university_contact, 
            show_university_location, 
            show_other_majors,
            get_university_data_by_id
        )
        from unittest.mock import AsyncMock
        
        # Test helper function
        university_data = get_university_data_by_id("rupp")
        if university_data:
            print(f"   ✅ University data found: {university_data.get('name', 'Unknown')}")
        else:
            print("   ⚠️ No university data found")
        
        # Test handlers
        query = AsyncMock()
        update = AsyncMock()
        update.callback_query = query
        context = AsyncMock()
        
        # Test contact handler
        query.data = "contact_rupp"
        try:
            await show_university_contact(update, context)
            print("   ✅ Contact handler works")
        except Exception as e:
            print(f"   ❌ Contact handler failed: {e}")
        
        # Test location handler
        query.data = "location_rupp"
        try:
            await show_university_location(update, context)
            print("   ✅ Location handler works")
        except Exception as e:
            print(f"   ❌ Location handler failed: {e}")
        
        # Test other majors handler
        query.data = "other_majors_rupp"
        try:
            await show_other_majors(update, context)
            print("   ✅ Other majors handler works")
        except Exception as e:
            print(f"   ❌ Other majors handler failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ University handlers test failed: {e}")
        return False


async def test_pdf_export():
    """Test PDF export functionality"""
    print("\n📄 Testing PDF Export...")
    
    try:
        from src.bot.handlers.details import generate_pdf_export
        from unittest.mock import AsyncMock
        
        query = AsyncMock()
        query.data = "pdf_export_computer-science"
        
        update = AsyncMock()
        update.callback_query = query
        
        context = AsyncMock()
        context.user_data = {
            'recommendations': [
                {
                    'major_id': 'computer-science',
                    'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                    'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ',
                    'city': 'ភ្នំពេញ',
                    'tuition_fees_usd': 800,
                    'employment_rate': '92%',
                    'mcda_score': 0.85,
                    'ml_score': 0.78
                }
            ]
        }
        
        try:
            await generate_pdf_export(update, context)
            print("   ✅ PDF export handler works")
            return True
        except Exception as e:
            print(f"   ❌ PDF export failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ PDF export test failed: {e}")
        return False


async def test_handler_registration():
    """Test handler registration"""
    print("\n🔧 Testing Handler Registration...")
    
    try:
        from src.bot.app import create_bot_application
        import os
        
        token = os.getenv('TELEGRAM_BOT_TOKEN', 'dummy_token')
        app = await create_bot_application(token)
        
        total_handlers = sum(len(handlers) for handlers in app.handlers.values())
        print(f"   ✅ Total handlers: {total_handlers}")
        
        # Check patterns
        callback_patterns = []
        for group_handlers in app.handlers.values():
            for handler in group_handlers:
                if hasattr(handler, 'pattern') and handler.pattern:
                    callback_patterns.append(handler.pattern.pattern)
        
        required_patterns = ['contact_', 'location_', 'other_majors_', 'pdf_export_']
        found_patterns = [p for p in required_patterns if any(p in cp for cp in callback_patterns)]
        
        print(f"   ✅ Found patterns: {found_patterns}")
        
        if len(found_patterns) == len(required_patterns):
            print("   ✅ All handlers registered")
            return True
        else:
            print(f"   ❌ Missing patterns: {set(required_patterns) - set(found_patterns)}")
            return False
            
    except Exception as e:
        print(f"❌ Handler registration test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Testing Critical Fixes\n")
    
    test1 = await test_university_handlers()
    test2 = await test_pdf_export()
    test3 = await test_handler_registration()
    
    print(f"\n📊 Results:")
    print(f"   University Handlers: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   PDF Export: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"   Handler Registration: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    overall_pass = test1 and test2 and test3
    print(f"\n🎯 Status: {'✅ ALL FIXES WORKING' if overall_pass else '❌ SOME FIXES NEEDED'}")
    
    if overall_pass:
        print("\n🎉 All critical issues are FIXED!")
        print("✅ University contact, location, other majors buttons work")
        print("✅ PDF export functionality works")
        print("✅ All handlers properly registered")
        print("\n🚀 Your bot is ready for the university presentation!")
    
    return overall_pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
