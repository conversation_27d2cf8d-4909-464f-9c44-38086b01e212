#!/usr/bin/env python3
"""
Test all critical fixes: Budget filtering, Contact/Location buttons, Detail view
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_budget_filtering_strict():
    """Test strict budget filtering - NO FALLBACK"""
    print("💰 Testing STRICT Budget Filtering...")
    
    try:
        from src.core.recommender import get_recommendations
        
        # Test $500 budget (index 0) - should return EMPTY if no programs within budget
        user_answers = {
            0: {'answer_index': 2, 'answer_text': 'Computer Science'},  # Interest
            1: {'answer_index': 1, 'answer_text': 'Medium'},             # Difficulty
            2: {'answer_index': 1, 'answer_text': '<PERSON>em Reap'},         # Location
            3: {'answer_index': 0, 'answer_text': 'តិចជាង $500'}         # Budget $500
        }
        
        recommendations = await get_recommendations(user_answers)
        print(f"✅ Generated: {len(recommendations)} recommendations for $500 budget")
        
        if len(recommendations) == 0:
            print("   ✅ CORRECT: No programs within $500 budget - no expensive fallbacks shown")
            return True
        else:
            # Check if any recommendations exceed $600 (500 + 20% tolerance)
            over_budget = []
            for rec in recommendations:
                fees = rec.get('tuition_fees_usd', rec.get('fees_usd', 0))
                if isinstance(fees, str):
                    try:
                        fees = float(fees.replace('$', '').replace(',', ''))
                    except:
                        fees = 0
                
                if fees > 600:  # $500 + 20% tolerance
                    over_budget.append((rec.get('major_name_kh', 'Unknown'), fees))
            
            if over_budget:
                print(f"   ❌ FOUND {len(over_budget)} OVER-BUDGET PROGRAMS:")
                for name, fee in over_budget[:3]:
                    print(f"      - {name}: ${fee}")
                return False
            else:
                print(f"   ✅ All {len(recommendations)} programs are within $600 budget")
                return True
                
    except Exception as e:
        print(f"❌ Budget filtering test failed: {e}")
        return False


async def test_contact_location_handlers():
    """Test contact and location button handlers"""
    print("\n📞 Testing Contact & Location Handlers...")
    
    try:
        from src.bot.handlers.details import show_university_contact, show_university_location
        from unittest.mock import AsyncMock
        
        # Test contact handler
        query = AsyncMock()
        query.data = "contact_angkor"
        
        update = AsyncMock()
        update.callback_query = query
        
        context = AsyncMock()
        
        # Test contact handler (should not crash)
        try:
            await show_university_contact(update, context)
            print("   ✅ Contact handler executed without errors")
            contact_works = True
        except Exception as e:
            print(f"   ❌ Contact handler failed: {e}")
            contact_works = False
        
        # Test location handler
        query.data = "location_angkor"
        try:
            await show_university_location(update, context)
            print("   ✅ Location handler executed without errors")
            location_works = True
        except Exception as e:
            print(f"   ❌ Location handler failed: {e}")
            location_works = False
        
        return contact_works and location_works
        
    except Exception as e:
        print(f"❌ Contact/Location handler test failed: {e}")
        return False


async def test_enhanced_detail_view():
    """Test enhanced detail view with comprehensive information"""
    print("\n🔍 Testing Enhanced Detail View...")
    
    try:
        from src.bot.handlers.details import show_major_details
        from unittest.mock import AsyncMock
        
        # Create mock objects with comprehensive data
        query = AsyncMock()
        query.data = "details_civil-engineering"
        
        update = AsyncMock()
        update.callback_query = query
        
        context = AsyncMock()
        context.user_data = {
            'recommendations': [
                {
                    'major_id': 'civil-engineering',
                    'major_name_kh': 'វិស្វកម្មសំណង់ស៊ីវិល',
                    'university_name_kh': 'សាកលវិទ្យាល័យអង្គរ',
                    'city': 'សៀមរាប',
                    'tuition_fees_usd': 1100,
                    'employment_rate': '87%',
                    'duration_years': 4,
                    'description_kh': 'មុខជំនាញវិស្វកម្មសំណង់ស៊ីវិល',
                    'career_prospects_kh': ['វិស្វករសំណង់', 'អ្នកគ្រប់គ្រងគម្រោង'],
                    'university_id': 'angkor',
                    'hybrid_score': 0.85,
                    'mcda_score': 0.80,
                    'ml_score': 0.90
                }
            ]
        }
        
        # Test the function
        try:
            await show_major_details(update, context)
            print("   ✅ Enhanced detail view executed without errors")
            print("   ✅ Should show: major, university, location, fees, employment rate, duration")
            print("   ✅ Should have: contact, location, other majors, back buttons")
            return True
        except Exception as detail_error:
            print(f"   ❌ Enhanced detail view failed: {detail_error}")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced detail view test failed: {e}")
        return False


async def test_handler_registration():
    """Test if all handlers are properly registered"""
    print("\n🔧 Testing Handler Registration...")
    
    try:
        from src.bot.app import create_bot_application
        import os
        
        # Use a dummy token for testing
        token = os.getenv('TELEGRAM_BOT_TOKEN', 'dummy_token_for_testing')
        
        app = await create_bot_application(token)
        
        # Count handlers
        total_handlers = sum(len(handlers) for handlers in app.handlers.values())
        print(f"   ✅ Total handlers registered: {total_handlers}")
        
        # Check for specific patterns
        callback_patterns = []
        for group_handlers in app.handlers.values():
            for handler in group_handlers:
                if hasattr(handler, 'pattern') and handler.pattern:
                    callback_patterns.append(handler.pattern.pattern)
        
        print(f"   ✅ Callback patterns: {len(callback_patterns)}")
        
        # Check for critical patterns
        required_patterns = [
            'details_',      # Detail view
            'contact_',      # Contact button
            'location_',     # Location button
            'other_majors_', # Other majors button
            'back_to_recommendations' # Back button
        ]
        
        missing_patterns = []
        for pattern in required_patterns:
            found = any(pattern in p for p in callback_patterns)
            if not found:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print(f"   ❌ Missing patterns: {missing_patterns}")
            return False
        else:
            print("   ✅ All critical button handlers are registered")
            return True
            
    except Exception as e:
        print(f"❌ Handler registration test failed: {e}")
        return False


async def main():
    """Run all critical tests"""
    print("🚀 Testing All Critical Fixes\n")
    
    test1 = await test_budget_filtering_strict()
    test2 = await test_contact_location_handlers()
    test3 = await test_enhanced_detail_view()
    test4 = await test_handler_registration()
    
    print(f"\n📊 Test Results:")
    print(f"   Strict Budget Filtering: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   Contact/Location Buttons: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"   Enhanced Detail View: {'✅ PASS' if test3 else '❌ FAIL'}")
    print(f"   Handler Registration: {'✅ PASS' if test4 else '❌ FAIL'}")
    
    overall_pass = test1 and test2 and test3 and test4
    print(f"\n🎯 Overall Status: {'✅ ALL CRITICAL FIXES WORKING' if overall_pass else '❌ SOME FIXES FAILED'}")
    
    if overall_pass:
        print("\n🎉 All critical issues are FIXED!")
        print("✅ Budget filtering: No expensive programs when budget is $500")
        print("✅ Contact buttons: Work properly with university data")
        print("✅ Location buttons: Show university location details")
        print("✅ Detail view: Comprehensive program information")
        print("✅ Navigation: All buttons properly registered")
        print("\n🚀 Your bot is ready for the university presentation!")
    else:
        print("\n⚠️  Some critical issues still need work.")
    
    return overall_pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
